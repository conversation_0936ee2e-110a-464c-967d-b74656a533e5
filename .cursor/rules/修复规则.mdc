---
description: 
globs: 
alwaysApply: false
---
#必须使用中文反馈
#问题修复规则：
按处理问题的方法步骤流程来执行
##必须考虑复用、或在现有方法上扩展、或进行方法重载，保证最小粒度改动，减少重复代码，考虑后续官方更新版本。

#方法步骤
为了帮助你更好解决报错代码，请认真参考一下文档：
##回顾《任务完成总结.md》查找是否有已经解决过的问题总结
##根据问题查看《Mem0二次开发技术方案设计.md》《Mem0智能记忆系统-项目梳理.md》
###查找项目相关代码块是否能解决当前的问题
##禁止乱创建修复脚本，和多余的，已经存在的文件。

#修复要求
##必须使用sequentialthinking工具分析问题
##使用firercawl_search MCP工具查找相关错误资料。
##禁止为了不报错，屏蔽功能，是要解决问题使功能正常
##禁止使用修复脚本、补丁、以及优雅解决，不要过多的创建额外文件
##不要做一些无关紧要的修改增加代码量、不要用简化功能的方式取代模块功能
##请分段直接读取源码文件修改并编辑
##必须使用中文回复
##避免路径混乱，文件符合统一管理的原则
##重构需要强制清除未使用的容器、镜像、依赖缓存

#注意事项
##修改文件代码后必须停止容器，重新构建服务来验证修复结果，同时清除未使用的容器和系统多余文件

#操作内容
##执行修复完成后，将这本次修复的问题、核心、过程和结果增量并且更新到.cursor/docs文件夹下的文件《任务完成总结.md》