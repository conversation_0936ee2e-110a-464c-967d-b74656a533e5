---
alwaysApply: false
---
# 技术方案设计文档规范

## 关键规则
- 技术方案文档必须遵循规定的章节结构，包括名词解释、领域模型、应用调用关系和详细方案设计四个主要部分
- 名词解释部分必须建立功能和技术的统一语言，确保术语简单易懂
- 功能模块需清晰表达功能之间调用关系，可使用UML图或ER图进行可视化
- 应用调用关系必须体现跨应用的接口调用关系
- 详细方案设计应按功能和模块流程进行分类，对每个接口的改动点、代码分层和文档变更进行详细说明
- 代码改动点需重点突出实现思路，而不仅是罗列代码变更
- 对外接口协议的所有变更（包括字段变更等）必须在接口文档中明确体现

```markdown
# 技术方案设计文档：[方案名称]
## 文档信息
- 作者：[作者姓名]
- 版本：[版本号，如v1.0]
- 日期：[创建/更新日期]
- 状态：[草稿/已评审/已确认]
- 架构类型：[GBF框架/非GBF框架] - 版本：[框架版本号]
# 一、名词解释
[建立功能和技术的统一语言，尽量简单易懂]
| 术语 | 解释 |
|------|------|
| 术语1 | 含义说明 |
| 术语2 | 含义说明 |
# 二、功能模块
[描述项目中的核心功能及其关系，推荐使用UML图表示]
## 核心功能
[列出核心功能及其属性、行为]
## 功能模块关系
[描述功能模块的关系，可使用ER图]
```mermaid
classDiagram
    Class01 <|-- AveryLongClass : Cool
    Class03 *-- Class04
    Class05 o-- Class06
    Class07 .. Class08
    Class09 --> C2 : Where am I?
    Class09 --* C3
    Class09 --|> Class07
    Class07 : equals()
    Class07 : Object[] elementData
    Class01 : size()
    Class01 : int chimp
    Class01 : int gorilla
    Class08 <--> C2: Cool label
```
# 三、应用调用关系示例
[体现跨应用的接口调用关系]
## 系统架构图
[系统整体架构图，展示系统组件和交互关系]
```mermaid
flowchart TD
    A[应用1] -->|接口调用| B[应用2]
    B -->|消息发送| C[消息队列]
    D[应用3] -->|消息消费| C
    D -->|数据存储| E[(数据库)]
```
## 时序图
[关键流程的时序图，展示组件间的交互顺序]
```mermaid
sequenceDiagram
    参与者A->>参与者B: 请求数据
    参与者B->>参与者C: 转发请求
    参与者C-->>参与者B: 返回数据
    参与者B-->>参与者A: 处理后返回
```
# 四、详细方案设计示例
## 架构选型
[说明本方案采用的架构模式，如标准三层架构等]
### 分层架构说明示例
[描述本方案的分层架构，说明各层职责]
#### 标准分层架构（非GBF框架项目）示例
```
# HTTP接口方式
- **Controller**层：处理HTTP请求，参数校验
- Service层：实现业务逻辑
- Repository层：数据访问和持久化
- Domain层：领域模型和业务规则
# HSF/RPC服务方式
- Provider层：服务提供者，定义和实现HSF服务接口
- Service层：实现业务逻辑
- Repository层：数据访问和持久化
- Domain层：领域模型和业务规则
```
### 数据模型设计示例
[描述数据模型的设计，包括不同层次的数据模型]示例
```
# 标准数据模型（非GBF框架项目）
- DTO(Data Transfer Object)：接口层数据传输对象
- BO(Business Object)：业务逻辑层数据对象
- DO(Domain Object)：领域模型对象
- PO(Persistent Object)：持久化对象
# GBF框架数据模型（GBF框架项目）
- DTO：对接客户端，透出业务流程结果
- DO：封装核心领域逻辑，定义服务入口
- PO：与数据库交互，屏蔽存储细节
```
## 应用1示例
### 业务流程1示例
#### xxx接口示例
**接口说明**：[详细说明接口的用途和功能]
**接口路径**：[HTTP方法] [路径] 或 [HSF服务接口定义]
**请求参数**：
```json
{
  "param1": "value1",
  "param2": "value2"
}
```
**返回结果**：
```json
{
  "code": 200,
  "message": "success",
  "data": {
    "field1": "value1",
    "field2": "value2"
  }
}
```
#### 接口改动点示例
[说明接口的改动类型：新增、能力调整、扩展等，并详述改动内容]
#### 代码分层设计示例
[描述代码的分层结构，确保符合工程规范]示例
##### 标准分层设计（非GBF框架项目）示例
```
# HTTP接口方式
- Controller层：处理HTTP请求，参数校验
  - 职责：参数校验、请求处理、结果封装
  - 代码位置：web包、controller包
  - 设计要点：保持轻量，不包含业务逻辑
- Service层：实现业务逻辑
  - 职责：实现业务逻辑、编排服务调用、事务管理
  - 代码位置：service包、manager包
  - 设计要点：聚合与编排，可包含复杂业务逻辑
- Repository层：数据访问和持久化
  - 职责：封装数据访问逻辑，实现数据持久化
  - 代码位置：repository包、dao包
  - 设计要点：封装数据库操作，提供数据访问接口
- Domain层：领域模型和业务规则
  - 职责：定义领域模型，实现领域规则
  - 代码位置：domain包、model包
  - 设计要点：领域驱动设计，封装核心业务规则
```
##### 框架分层设计示例
# 定义示例（Platform层）
- 职责：编排NodeService，形成完整业务流程
- 代码位置：platform包
- 设计要点：
  - Process作为最上层逻辑，直接承接接口请求
  - 通过Spring Bean声明Process流程配置
  - Process只能调用NodeService，不能跨层调用
  - 不应包含具体业务逻辑，专注于流程编排
# 实现示例
- 职责：组合多个DomainService，形成标准化服务入口
- 代码位置：node包
- 设计要点：
  - NodeService作为标准化服务入口
  - 禁止NodeService之间相互调用
  - 通过扩展点控制节点级逻辑(如前置校验)
  - 不应包含复杂业务逻辑，主要负责编排
# 扩展点设计示例
- 职责：定义扩展点接口和实现
- 代码位置：
  - 接口定义：ability包
  - 行业实现：app包下对应行业目录
  - 场景实现：domain/node包下对应scenario目录
- 设计要点：
  - 统一在Ability层声明扩展点接口
  - 行业定制实现放在App层(如/app/food/)
  - 场景定制实现放在Domain/Node包下
  - 扩展点必须有默认实现，保证基础功能可用
```
##### 路由条件设计示例
[说明扩展点的路由条件设计]
```
# 路由维度
- 业务维度（BizCode）：区分不同行业，如"FOOD"、"RETAIL"
- 场景维度（Scenario）：区分不同场景，如"C2C"、"B2C"
- 其他维度：用户类型、渠道等
# 路由策略
- 优先级1：精确匹配（BizCode=A + Scenario=B）
- 优先级2：业务码匹配（BizCode=A）
- 优先级3：场景码匹配（Scenario=B）
- 优先级4：默认实现
# 降级策略
如果找不到满足条件的扩展点实现，按优先级顺序降级匹配，
直到找到默认实现
```
#### 代码改动点示例
[详述需要改动的代码，重点说明实现思路]
1. Controller/Provider层改动：
   - 新增XX控制器/服务提供者
   - 修改YY方法参数
2. Service层改动：
   - 新增XX服务
   - 调整YY逻辑处理流程
3. GBF框架特定改动(GBF框架项目)：
   - 新增Process流程定义
   - 新增NodeService节点服务
   - 新增扩展点接口与实现
   - 修改扩展点路由规则
## 数据库变更示例
### 表结构设计示例
[描述需要新增或修改的数据库表结构]
#### 表名：[表名]
| 字段名 | 数据类型 | 是否为空 | 主键 | 注释 |
|-------|---------|---------|------|------|
| id | bigint | 否 | 是 | 主键ID |
| ... | ... | ... | ... | ... |
### 索引设计示例
[描述需要新增或修改的索引]
| 索引名 | 字段 | 索引类型 | 说明 |
|-------|------|---------|------|
| idx_xxx | 字段1, 字段2 | 普通/唯一/主键 | 索引说明 |
## 接口文档变更示例
[描述需要新增或修改的接口文档]
### 接口名：[接口名]
- 接口路径：[HTTP方法] [路径] 或 [HSF服务接口定义]
- 变更类型：[新增/修改/删除]
- 变更说明：[详细说明接口变更]
## 配置变更示例
[描述需要新增或修改的配置]
### 配置类型：[配置类型]
- 配置名：[配置名]
- 配置值：[配置值]
- 说明：[配置说明]
## 非功能性需求
### 性能需求
[描述性能需求，如响应时间、并发量等]
### 可用性需求
[描述可用性需求，如系统可用率、故障恢复能力等]
### 扩展性需求
[描述扩展性需求，如系统的可扩展性、可伸缩性等]
## 兼容性与平滑迁移方案
[描述系统升级的兼容性问题及平滑迁移方案]
### 兼容性问题
[描述可能的兼容性问题]
### 平滑迁移方案
[描述平滑迁移的方案]
## 风险与应对措施
[描述可能的风险及应对措施]
| 风险 | 可能性 | 影响 | 应对措施 |
|------|-------|------|---------|
| 风险1 | 高/中/低 | 高/中/低 | 应对措施1 |
| 风险2 | 高/中/低 | 高/中/低 | 应对措施2 |
```
## 方案设计工作流程示例
1. **架构识别阶段**
   - 确定项目使用的架构类型(GBF/非GBF)
   - 识别关键架构组件和分层结构
   - 确定方案设计重点和特殊要求
   - 选择适合的方案模板
2. **需求分析阶段**
   - 确定功能边界和核心业务流程
   - 识别核心业务实体和领域模型
   - 确定接口定义和数据结构
   - 识别可能的扩展点和变化点
3. **方案设计阶段**
   - 根据架构特点进行分层设计