export type Category = "personal" | "work" | "health" | "finance" | "travel" | "education" | "preferences" | "relationships" | "culture" | "interests" | "weather"
export type Client = "chrome" | "chatgpt" | "cursor" | "windsurf" | "terminal" | "api"

export interface Memory {
  id: string
  memory: string
  metadata: any
  client: Client
  categories: Category[]
  created_at: number | string
  app_name: string
  user_id?: string
  state: "active" | "paused" | "archived" | "deleted"
}