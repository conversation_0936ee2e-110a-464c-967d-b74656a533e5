'use client';

import React, { useState } from 'react';
import { Filter, X, <PERSON>, <PERSON>Off, Settings } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';

export interface ActivityFilterConfig {
  // 操作类型过滤
  showAdd: boolean;
  showSearch: boolean;
  showUpdate: boolean;
  showDelete: boolean;
  
  // 智能过滤
  hideUIOperations: boolean;        // 隐藏UI操作（页面刷新、搜索等）
  hideSystemOperations: boolean;    // 隐藏系统操作
  hideEmptyMetadata: boolean;       // 隐藏空metadata的记录
  hideDuplicateSearches: boolean;   // 隐藏重复搜索
  
  // 时间过滤
  timeRange: 'all' | '1h' | '24h' | '7d' | '30d';
  
  // 用户过滤
  specificUsers: string[];
  hideAnonymousUsers: boolean;
  
  // 响应时间过滤
  minResponseTime: number;
  maxResponseTime: number;
  
  // 内容过滤
  excludeKeywords: string[];
  includeKeywords: string[];
}

interface ActivityFilterProps {
  config: ActivityFilterConfig;
  onConfigChange: (config: ActivityFilterConfig) => void;
  activityStats?: {
    total: number;
    filtered: number;
    operations: Record<string, number>;
  };
}

const defaultConfig: ActivityFilterConfig = {
  showAdd: true,
  showSearch: false,  // 默认隐藏搜索操作
  showUpdate: true,
  showDelete: true,
  hideUIOperations: true,     // 默认隐藏UI操作
  hideSystemOperations: true, // 默认隐藏系统操作
  hideEmptyMetadata: true,    // 默认隐藏空metadata
  hideDuplicateSearches: true, // 默认隐藏重复搜索
  timeRange: '24h',
  specificUsers: [],
  hideAnonymousUsers: false,
  minResponseTime: 0,
  maxResponseTime: 10000,
  excludeKeywords: ['refresh', 'ui_load', 'page_view', 'heartbeat'],
  includeKeywords: []
};

const ActivityFilter: React.FC<ActivityFilterProps> = ({
  config,
  onConfigChange,
  activityStats
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [newKeyword, setNewKeyword] = useState('');

  const updateConfig = (updates: Partial<ActivityFilterConfig>) => {
    onConfigChange({ ...config, ...updates });
  };

  const addExcludeKeyword = () => {
    if (newKeyword.trim() && !config.excludeKeywords.includes(newKeyword.trim())) {
      updateConfig({
        excludeKeywords: [...config.excludeKeywords, newKeyword.trim()]
      });
      setNewKeyword('');
    }
  };

  const removeExcludeKeyword = (keyword: string) => {
    updateConfig({
      excludeKeywords: config.excludeKeywords.filter(k => k !== keyword)
    });
  };

  const resetToDefaults = () => {
    onConfigChange(defaultConfig);
  };

  const getFilterSummary = () => {
    const activeFilters = [];
    if (!config.showSearch) activeFilters.push('隐藏搜索');
    if (config.hideUIOperations) activeFilters.push('隐藏UI操作');
    if (config.hideEmptyMetadata) activeFilters.push('隐藏空数据');
    if (config.timeRange !== 'all') activeFilters.push(`时间范围: ${config.timeRange}`);
    return activeFilters;
  };

  return (
    <Card className="bg-zinc-900 border-zinc-800">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="text-white flex items-center gap-2">
            <Filter className="w-5 h-5 text-[#00d4aa]" />
            智能活动过滤器
            {activityStats && (
              <span className="text-sm text-zinc-400 font-normal">
                ({activityStats.filtered}/{activityStats.total} 条记录)
              </span>
            )}
          </CardTitle>
          
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={resetToDefaults}
              className="text-zinc-400 hover:text-white"
            >
              重置
            </Button>
            
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-zinc-400 hover:text-white"
            >
              <Settings className="w-4 h-4 mr-1" />
              {isExpanded ? '收起' : '展开'}
            </Button>
          </div>
        </div>
        
        {/* 过滤器摘要 */}
        <div className="flex flex-wrap gap-2 mt-2">
          {getFilterSummary().map((filter, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {filter}
            </Badge>
          ))}
        </div>
      </CardHeader>

      {isExpanded && (
        <CardContent className="space-y-6">
          {/* 操作类型过滤 */}
          <div>
            <h3 className="text-sm font-medium text-white mb-3">操作类型</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-add"
                  checked={config.showAdd}
                  onCheckedChange={(checked) => updateConfig({ showAdd: checked })}
                />
                <Label htmlFor="show-add" className="text-sm text-zinc-300">
                  添加记忆
                  {activityStats?.operations.ADD && (
                    <span className="text-xs text-zinc-500 ml-1">
                      ({activityStats.operations.ADD})
                    </span>
                  )}
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-search"
                  checked={config.showSearch}
                  onCheckedChange={(checked) => updateConfig({ showSearch: checked })}
                />
                <Label htmlFor="show-search" className="text-sm text-zinc-300">
                  搜索操作
                  {activityStats?.operations.SEARCH && (
                    <span className="text-xs text-zinc-500 ml-1">
                      ({activityStats.operations.SEARCH})
                    </span>
                  )}
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-update"
                  checked={config.showUpdate}
                  onCheckedChange={(checked) => updateConfig({ showUpdate: checked })}
                />
                <Label htmlFor="show-update" className="text-sm text-zinc-300">
                  更新记忆
                  {activityStats?.operations.UPDATE && (
                    <span className="text-xs text-zinc-500 ml-1">
                      ({activityStats.operations.UPDATE})
                    </span>
                  )}
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="show-delete"
                  checked={config.showDelete}
                  onCheckedChange={(checked) => updateConfig({ showDelete: checked })}
                />
                <Label htmlFor="show-delete" className="text-sm text-zinc-300">
                  删除记忆
                  {activityStats?.operations.DELETE && (
                    <span className="text-xs text-zinc-500 ml-1">
                      ({activityStats.operations.DELETE})
                    </span>
                  )}
                </Label>
              </div>
            </div>
          </div>

          {/* 智能过滤 */}
          <div>
            <h3 className="text-sm font-medium text-white mb-3">智能过滤</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div className="flex items-center space-x-2">
                <Switch
                  id="hide-ui"
                  checked={config.hideUIOperations}
                  onCheckedChange={(checked) => updateConfig({ hideUIOperations: checked })}
                />
                <Label htmlFor="hide-ui" className="text-sm text-zinc-300">
                  隐藏UI操作 (页面刷新、加载等)
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="hide-system"
                  checked={config.hideSystemOperations}
                  onCheckedChange={(checked) => updateConfig({ hideSystemOperations: checked })}
                />
                <Label htmlFor="hide-system" className="text-sm text-zinc-300">
                  隐藏系统操作
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="hide-empty"
                  checked={config.hideEmptyMetadata}
                  onCheckedChange={(checked) => updateConfig({ hideEmptyMetadata: checked })}
                />
                <Label htmlFor="hide-empty" className="text-sm text-zinc-300">
                  隐藏空数据记录
                </Label>
              </div>
              
              <div className="flex items-center space-x-2">
                <Switch
                  id="hide-duplicate"
                  checked={config.hideDuplicateSearches}
                  onCheckedChange={(checked) => updateConfig({ hideDuplicateSearches: checked })}
                />
                <Label htmlFor="hide-duplicate" className="text-sm text-zinc-300">
                  隐藏重复搜索
                </Label>
              </div>
            </div>
          </div>

          {/* 时间范围 */}
          <div>
            <h3 className="text-sm font-medium text-white mb-3">时间范围</h3>
            <div className="flex flex-wrap gap-2">
              {[
                { value: 'all', label: '全部' },
                { value: '1h', label: '1小时' },
                { value: '24h', label: '24小时' },
                { value: '7d', label: '7天' },
                { value: '30d', label: '30天' }
              ].map((option) => (
                <Button
                  key={option.value}
                  variant={config.timeRange === option.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateConfig({ timeRange: option.value as any })}
                  className={config.timeRange === option.value 
                    ? "bg-[#00d4aa] text-black" 
                    : "border-zinc-700 text-zinc-300 hover:bg-zinc-800"
                  }
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* 排除关键词 */}
          <div>
            <h3 className="text-sm font-medium text-white mb-3">排除关键词</h3>
            <div className="space-y-3">
              <div className="flex gap-2">
                <input
                  type="text"
                  value={newKeyword}
                  onChange={(e) => setNewKeyword(e.target.value)}
                  onKeyPress={(e) => e.key === 'Enter' && addExcludeKeyword()}
                  placeholder="添加要排除的关键词..."
                  className="flex-1 bg-zinc-800 border border-zinc-700 rounded px-3 py-2 text-sm text-zinc-300 placeholder-zinc-500"
                />
                <Button
                  onClick={addExcludeKeyword}
                  size="sm"
                  className="bg-[#00d4aa] text-black hover:bg-[#00b89a]"
                >
                  添加
                </Button>
              </div>
              
              <div className="flex flex-wrap gap-2">
                {config.excludeKeywords.map((keyword) => (
                  <Badge
                    key={keyword}
                    variant="secondary"
                    className="flex items-center gap-1 text-xs"
                  >
                    {keyword}
                    <X
                      className="w-3 h-3 cursor-pointer hover:text-red-400"
                      onClick={() => removeExcludeKeyword(keyword)}
                    />
                  </Badge>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  );
};

export { ActivityFilter, defaultConfig };
