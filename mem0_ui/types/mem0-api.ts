/**
 * Mem0 API类型定义
 * 基于实际Mem0 Server OpenAPI规范的接口类型
 */

// 基础消息类型
export interface Message {
  role: string;
  content: string | object | object[];
}

// 记忆相关类型
export interface Mem0Memory {
  id: string;
  memory: string;
  text?: string; // 兼容性字段
  hash?: string;
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  custom_categories?: string[];
  created_at?: string;
  updated_at?: string;
  metadata?: Record<string, any>;
  categories?: string[];
  score?: number;
  state?: 'active' | 'paused' | 'archived' | 'deleted';
  app_name?: string;
}

// 创建记忆请求
export interface MemoryCreateRequest {
  messages?: Message[];
  text?: string; // 兼容性字段
  user_id?: string;
  agent_id?: string;
  run_id?: string;
  app_name?: string;
  infer?: boolean;
  metadata?: Record<string, any>;
  custom_categories?: Array<Record<string, string>>;
  custom_instructions?: string;
  version?: string;
  includes?: string;
  excludes?: string;
  timestamp?: number;
  output_format?: string;
}

// 更新记忆请求
export interface UpdateMemoryRequest {
  text?: string;
  state?: 'active' | 'paused' | 'archived' | 'deleted';
  metadata?: Record<string, any>;
}

// 搜索记忆请求
export interface SearchRequest {
  query?: string;
  user_id?: string;
  run_id?: string;
  agent_id?: string;
  limit?: number;
  filters?: Record<string, any>;
  keyword_search?: boolean;
  rerank?: boolean;
  filter_memories?: boolean;
  output_format?: string;
}

// V2 API请求类型
export interface V2MemoriesRequest {
  filters?: Record<string, any>;
  limit?: number;
}

export interface V2SearchRequest {
  query: string;
  filters?: Record<string, any>;
  limit?: number;
  keyword_search?: boolean;
  rerank?: boolean;
  filter_memories?: boolean;
}

// 批量操作类型
export interface BatchUpdateRequest {
  operation?: string;
  memories?: Array<{
    memory_id: string;
    text: string;
    [key: string]: string;
  }>;
  memory_ids?: string[];
  data?: Record<string, any>;
}

export interface BatchDeleteRequest {
  operation?: string;
  memories?: Array<{
    memory_id: string;
    [key: string]: string;
  }>;
  memory_ids?: string[];
}

// 反馈类型
export interface FeedbackRequest {
  memory_id: string;
  feedback?: string;
  feedback_reason?: string;
}

// 导出类型
export interface ExportRequest {
  schema: Record<string, any>;
  filters?: Record<string, any>;
  processing_instruction?: string;
}

// 错误类型
export interface HTTPValidationError {
  detail: Array<{
    loc: (string | number)[];
    msg: string;
    type: string;
  }>;
}

// API客户端配置
export interface Mem0ClientConfig {
  baseUrl: string;
  apiKey?: string;
  timeout?: number;
  retries?: number;
}

// 健康检查响应
export interface HealthCheckResponse {
  status: string;
  timestamp: string;
}

// 记忆历史项
export interface MemoryHistoryItem {
  id: string;
  memory_id: string;
  operation: 'create' | 'update' | 'delete';
  timestamp: string;
  user_id?: string;
  run_id?: string;
  agent_id?: string;
  custom_categories?: string[];
  changes?: Record<string, any>;
  previous_value?: any;
  new_value?: any;
}

// 用户管理
export interface UserInfo {
  id: string;
  name?: string;
  email?: string;
  memory_count: number;
  last_activity?: string;
  metadata?: Record<string, any>;
}

// APP/运行实例信息
export interface AppInfo {
  id: string;
  name?: string;
  user_id?: string;
  memory_count: number;
  last_activity?: string;
}

// 智能体信息
export interface AgentInfo {
  id: string;
  name?: string;
  user_id?: string;
  memory_count: number;
  last_activity?: string;
}

// 统计数据
export interface Mem0Stats {
  total_memories: number;
  active_memories?: number;
  archived_memories?: number;
  total_users: number;
  memories_today?: number;
  memory_operations_today?: number;
  avg_response_time: number;
  top_apps?: Array<{
    id: string;
    name: string;
    memory_count: number;
  }>;
}

// 错误类型
export interface Mem0ApiError {
  message: string;
  status?: number;
  code?: string;
}

// 搜索请求类型
export interface Mem0MemorySearchRequest {
  query?: string;
  user_id?: string;
  run_id?: string;
  agent_id?: string;
  limit?: number;
  filters?: Record<string, any>;
}

// 用户类型
export interface Mem0User {
  id: string;
  name?: string;
  email?: string;
  memory_count?: number;
  last_activity?: string;
  created_at?: string;
  metadata?: Record<string, any>;
}

// 活动类型
export interface Mem0Activity {
  id: string;
  memory_id: string;
  operation: 'create' | 'update' | 'delete' | 'access' | 'search' | 'retrieve';
  timestamp: string;
  user_id?: string;
  run_id?: string;
  agent_id?: string;
  app_name?: string;
  response_time?: number;
  metadata?: Record<string, any>;
  details?: Record<string, any>;
}

// 应用详情类型
export interface AppDetails {
  id: string;
  name: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  total_memories_created: number;
  total_memories_accessed: number;
  first_accessed?: string;
  last_accessed?: string;
}



// ============================================================================
// P0 Priority UI Management APIs (已实现于 server/main.py)
// ============================================================================

// 统计数据API响应类型 (/v1/stats)
export interface UIStatsResponse {
  total_memories: number;
  total_users: number;
  search_events: number;          // 替换平均响应时间
  add_events: number;             // 替换活跃用户数
  last_updated: string;
  time_range: string;
}

// 活动记录项类型
export interface UIActivityItem {
  id: string;
  timestamp: string;
  operation: 'SEARCH' | 'ADD' | 'UPDATE' | 'DELETE' | 'GRAPH_CREATE';
  details: string;
  response_time?: string;
  status: 'success' | 'error' | 'pending';
  user_id?: string;
  memory_id?: string;
  metadata?: Record<string, any>;
  request_payload?: string;
  action_type?: string;
}

// 活动日志API响应类型 (/v1/activities)
export interface UIActivitiesResponse {
  activities: UIActivityItem[];
  total: number;
  has_more: boolean;
  time_range: {
    start: string;
    end: string;
  };
}

// 快速操作按钮
export interface UIQuickAction {
  id: string;
  label: string;
  icon: string;
  primary?: boolean;
}

// 统一管理面板API响应类型 (/v1/admin/dashboard)
export interface UIDashboardResponse {
  stats: UIStatsResponse;
  recent_activities: UIActivityItem[];
  quick_actions: UIQuickAction[];
  last_updated: string;
}

// 统计数据查询参数
export interface UIStatsQueryParams {
  user_id?: string;
  time_range?: '1h' | '24h' | '7d' | '30d';
}

// 活动日志查询参数
export interface UIActivitiesQueryParams {
  user_id?: string;
  limit?: number;
  offset?: number;
  operation_type?: string;
  start_time?: string;
  end_time?: string;
}

// ============================================================================
// P1 Priority User Management APIs (用户管理API类型)
// ============================================================================

// 用户统计信息
export interface UIUserStats {
  user_id: string;
  total_memories: number;
  search_events: number;
  add_events: number;
  activity_score: number;
  agent_usage: Record<string, number>;
  last_activity?: string;
  created_at?: string;
  activity_timeline: Array<{
    operation: string;
    timestamp: string;
    count: number;
  }>;
}

// 用户信息（列表项）
export interface UIUserInfo {
  user_id: string;
  total_memories: number;
  last_activity?: string;
  created_at?: string;
  stats?: UIUserStats;
}

// 用户列表API响应 (/v1/users)
export interface UIUsersResponse {
  users: UIUserInfo[];
  total: number;
  has_more: boolean;
  pagination: {
    limit: number;
    offset: number;
    total_pages: number;
    current_page: number;
  };
}

// 用户删除API响应 (/v1/users/{user_id})
export interface UIUserDeleteResponse {
  message: string;
  user_id: string;
  deleted_counts: {
    memories: number;
    activities: number;
  };
  deleted_at: string;
  warning: string;
}

// 用户查询参数
export interface UIUsersQueryParams {
  limit?: number;
  offset?: number;
  include_stats?: boolean;
  time_range?: '1h' | '24h' | '7d' | '30d';
}


