# Mem0 UI 部署指南

## 概述

本指南提供了Mem0 UI的完整部署流程，包括开发环境配置、生产环境部署和故障排除。

## 环境要求

### 系统要求

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### 依赖服务

- **Mem0 Server**: 必须在端口8000运行
- **浏览器**: Chrome 90+, Firefox 88+, Safari 14+, Edge 90+

## 开发环境配置

### 1. 克隆项目

```bash
git clone https://github.com/mem0ai/mem0.git
cd mem0/openmemory/ui
```

### 2. 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 3. 环境变量配置

创建 `.env.local` 文件：

```env
# Mem0 Server配置
NEXT_PUBLIC_MEM0_API_URL=http://localhost:8000
NEXT_PUBLIC_API_TIMEOUT=10000

# 开发环境配置
NODE_ENV=development
NEXT_PUBLIC_DEBUG=true

# 可选配置
NEXT_PUBLIC_DEFAULT_USER_ID=default
NEXT_PUBLIC_DEFAULT_AGENT_ID=default
```

### 4. 启动Mem0 Server

```bash
# 在项目根目录
mem0 server start

# 或使用Python
python -m mem0.server.main
```

验证服务器状态：

```bash
curl http://localhost:8000/v1/health/
```

### 5. 启动开发服务器

```bash
npm run dev
```

访问 http://localhost:3000 查看应用。

## 生产环境部署

### 1. 构建应用

```bash
# 安装依赖
npm ci

# 构建生产版本
npm run build
```

### 2. 环境变量配置

生产环境 `.env.production` 文件：

```env
# 生产环境Mem0 Server地址
NEXT_PUBLIC_MEM0_API_URL=https://your-mem0-server.com
NEXT_PUBLIC_API_TIMEOUT=15000

# 生产环境配置
NODE_ENV=production
NEXT_PUBLIC_DEBUG=false

# 安全配置
NEXT_PUBLIC_SECURE_COOKIES=true
```

### 3. 部署选项

#### 选项1: Vercel部署

```bash
# 安装Vercel CLI
npm i -g vercel

# 部署
vercel --prod
```

#### 选项2: Docker部署

创建 `Dockerfile`：

```dockerfile
FROM node:18-alpine AS base

# 安装依赖
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json ./
RUN npm ci

# 构建应用
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npm run build

# 生产镜像
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

构建和运行：

```bash
# 构建镜像
docker build -t mem0-ui .

# 运行容器
docker run -p 3000:3000 \
  -e NEXT_PUBLIC_MEM0_API_URL=http://localhost:8000 \
  mem0-ui
```

#### 选项3: 静态导出

```bash
# 修改next.config.js
module.exports = {
  output: 'export',
  trailingSlash: true,
  images: {
    unoptimized: true
  }
}

# 构建静态文件
npm run build

# 部署到静态托管服务
# 输出目录: out/
```

### 4. 反向代理配置

#### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;

    # UI应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:8000/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## 性能优化

### 1. 构建优化

```javascript
// next.config.js
module.exports = {
  // 启用压缩
  compress: true,
  
  // 图片优化
  images: {
    domains: ['localhost'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // 实验性功能
  experimental: {
    optimizeCss: true,
    optimizePackageImports: ['@/components', '@/lib'],
  },
  
  // Webpack优化
  webpack: (config, { isServer }) => {
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
      };
    }
    return config;
  },
};
```

### 2. 缓存策略

```javascript
// 在next.config.js中配置缓存头
module.exports = {
  async headers() {
    return [
      {
        source: '/static/:path*',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};
```

### 3. 代码分割

```typescript
// 动态导入大型组件
import dynamic from 'next/dynamic';

const HeavyComponent = dynamic(() => import('@/components/HeavyComponent'), {
  loading: () => <div>Loading...</div>,
  ssr: false,
});
```

## 监控和日志

### 1. 应用监控

```typescript
// lib/monitoring.ts
export const trackEvent = (event: string, data?: any) => {
  if (process.env.NODE_ENV === 'production') {
    // 发送到监控服务
    console.log('Event:', event, data);
  }
};

export const trackError = (error: Error, context?: any) => {
  if (process.env.NODE_ENV === 'production') {
    // 发送到错误监控服务
    console.error('Error:', error, context);
  }
};
```

### 2. 性能监控

```typescript
// 在_app.tsx中添加性能监控
export function reportWebVitals(metric: NextWebVitalsMetric) {
  if (process.env.NODE_ENV === 'production') {
    // 发送性能指标到监控服务
    console.log(metric);
  }
}
```

### 3. 健康检查端点

```typescript
// pages/api/health.ts
export default function handler(req: NextApiRequest, res: NextApiResponse) {
  res.status(200).json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: process.env.npm_package_version,
  });
}
```

## 故障排除

### 常见问题

#### 1. 构建失败

**问题**: `npm run build` 失败

**解决方案**:
```bash
# 清理缓存
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

#### 2. API连接失败

**问题**: 无法连接到Mem0 Server

**检查步骤**:
```bash
# 1. 检查Mem0 Server状态
curl http://localhost:8000/v1/health/

# 2. 检查环境变量
echo $NEXT_PUBLIC_MEM0_API_URL

# 3. 检查网络连接
telnet localhost 8000
```

#### 3. 样式不加载

**问题**: CSS样式不生效

**解决方案**:
```bash
# 重新安装Tailwind CSS
npm uninstall tailwindcss
npm install -D tailwindcss@latest
npx tailwindcss init -p
```

#### 4. 内存不足

**问题**: 构建时内存不足

**解决方案**:
```bash
# 增加Node.js内存限制
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

### 调试技巧

#### 1. 启用详细日志

```bash
# 开发环境
DEBUG=* npm run dev

# 生产环境
NODE_ENV=production DEBUG=mem0:* npm start
```

#### 2. 网络调试

```typescript
// 在API客户端中添加请求日志
axios.interceptors.request.use(request => {
  console.log('Starting Request:', request);
  return request;
});

axios.interceptors.response.use(
  response => {
    console.log('Response:', response);
    return response;
  },
  error => {
    console.error('Response Error:', error);
    return Promise.reject(error);
  }
);
```

#### 3. 性能分析

```bash
# 分析包大小
npm run build
npx @next/bundle-analyzer

# 性能分析
npm run dev -- --profile
```

## 安全配置

### 1. 环境变量安全

```bash
# 生产环境不要暴露敏感信息
# ❌ 错误
NEXT_PUBLIC_API_SECRET=secret123

# ✅ 正确
API_SECRET=secret123  # 服务端变量
NEXT_PUBLIC_API_URL=https://api.example.com  # 公开变量
```

### 2. CSP配置

```javascript
// next.config.js
module.exports = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Content-Security-Policy',
            value: "default-src 'self'; script-src 'self' 'unsafe-eval'; style-src 'self' 'unsafe-inline';",
          },
        ],
      },
    ];
  },
};
```

### 3. HTTPS配置

```bash
# 生产环境强制HTTPS
# 在nginx或负载均衡器配置SSL证书
# 使用Let's Encrypt免费证书
certbot --nginx -d your-domain.com
```

## 更新和维护

### 1. 依赖更新

```bash
# 检查过时的依赖
npm outdated

# 更新依赖
npm update

# 安全更新
npm audit fix
```

### 2. 版本管理

```bash
# 使用语义化版本
npm version patch  # 1.0.0 -> 1.0.1
npm version minor  # 1.0.0 -> 1.1.0
npm version major  # 1.0.0 -> 2.0.0
```

### 3. 备份策略

- 定期备份配置文件
- 版本控制所有代码变更
- 数据库备份（如果使用）
- 监控日志备份

## 支持和联系

- **文档**: [docs/components/README.md](../components/README.md)
- **API文档**: [docs/api/mem0-server-api.md](../api/mem0-server-api.md)
- **问题报告**: GitHub Issues
- **社区支持**: Discord频道

---

**最后更新**: 2024-01-28  
**版本**: v1.0.0
