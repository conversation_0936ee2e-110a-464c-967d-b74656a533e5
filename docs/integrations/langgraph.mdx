---
title: LangGraph
---

<Snippet file="security-compliance.mdx" />

Build a personalized Customer Support AI Agent using LangGraph for conversation flow and Mem0 for memory retention. This integration enables context-aware and efficient support experiences.

## Overview

In this guide, we'll create a Customer Support AI Agent that:
1. Uses LangGraph to manage conversation flow
2. Leverages Mem0 to store and retrieve relevant information from past interactions
3. Provides personalized responses based on user history

## Setup and Configuration

Install necessary libraries:

```bash
pip install langgraph langchain-openai mem0ai
```


Import required modules and set up configurations:

<Note>Remember to get the Mem0 API key from [Mem0 Platform](https://app.mem0.ai).</Note>

```python
from typing import Annotated, TypedDict, List
from langgraph.graph import StateGraph, START
from langgraph.graph.message import add_messages
from langchain_openai import ChatOpenAI
from mem0 import MemoryClient
from langchain_core.messages import SystemMessage, HumanMessage, AIMessage

# Configuration
OPENAI_API_KEY = 'sk-xxx'  # Replace with your actual OpenAI API key
MEM0_API_KEY = 'your-mem0-key'  # Replace with your actual Mem0 API key

# Initialize <PERSON><PERSON>hain and Mem0
llm = ChatOpenAI(model="gpt-4", api_key=OPENAI_API_KEY)
mem0 = MemoryClient(api_key=MEM0_API_KEY)
```

## Define State and Graph

Set up the conversation state and LangGraph structure:

```python
class State(TypedDict):
    messages: Annotated[List[HumanMessage | AIMessage], add_messages]
    mem0_user_id: str

graph = StateGraph(State)
```

## Create Chatbot Function

Define the core logic for the Customer Support AI Agent:

```python
def chatbot(state: State):
    messages = state["messages"]
    user_id = state["mem0_user_id"]

    # Retrieve relevant memories
    memories = mem0.search(messages[-1].content, user_id=user_id)

    context = "Relevant information from previous conversations:\n"
    for memory in memories:
        context += f"- {memory['memory']}\n"

    system_message = SystemMessage(content=f"""You are a helpful customer support assistant. Use the provided context to personalize your responses and remember user preferences and past interactions.
{context}""")

    full_messages = [system_message] + messages
    response = llm.invoke(full_messages)

    # Store the interaction in Mem0
    mem0.add(f"User: {messages[-1].content}\nAssistant: {response.content}", user_id=user_id)
    return {"messages": [response]}
```

## Set Up Graph Structure

Configure the LangGraph with appropriate nodes and edges:

```python
graph.add_node("chatbot", chatbot)
graph.add_edge(START, "chatbot")
graph.add_edge("chatbot", "chatbot")

compiled_graph = graph.compile()
```

## Create Conversation Runner

Implement a function to manage the conversation flow:

```python
def run_conversation(user_input: str, mem0_user_id: str):
    config = {"configurable": {"thread_id": mem0_user_id}}
    state = {"messages": [HumanMessage(content=user_input)], "mem0_user_id": mem0_user_id}

    for event in compiled_graph.stream(state, config):
        for value in event.values():
            if value.get("messages"):
                print("Customer Support:", value["messages"][-1].content)
                return
```

## Main Interaction Loop

Set up the main program loop for user interaction:

```python
if __name__ == "__main__":
    print("Welcome to Customer Support! How can I assist you today?")
    mem0_user_id = "customer_123"  # You can generate or retrieve this based on your user management system
    while True:
        user_input = input("You: ")
        if user_input.lower() in ['quit', 'exit', 'bye']:
            print("Customer Support: Thank you for contacting us. Have a great day!")
            break
        run_conversation(user_input, mem0_user_id)
```

## Key Features

1. **Memory Integration**: Uses Mem0 to store and retrieve relevant information from past interactions.
2. **Personalization**: Provides context-aware responses based on user history.
3. **Flexible Architecture**: LangGraph structure allows for easy expansion of the conversation flow.
4. **Continuous Learning**: Each interaction is stored, improving future responses.

## Conclusion

By integrating LangGraph with Mem0, you can build a personalized Customer Support AI Agent that can maintain context across interactions and provide personalized assistance.

## Help

- For more details on LangGraph, visit the [LangChain documentation](https://python.langchain.com/docs/langgraph).
- [Mem0 Platform](https://app.mem0.ai/).
- If you need further assistance, please feel free to reach out to us through following methods:

<Snippet file="get-help.mdx" />
