# Mem0 UI 组件文档

## 概述

本文档描述了Mem0 UI项目中开发的核心组件，包括使用方法、API接口和最佳实践。

## 组件列表

### 1. Mem0StatsDashboard - 统计面板组件

#### 功能描述

Mem0StatsDashboard是一个四卡片布局的统计面板组件，用于展示系统的关键指标和实时数据。

#### 使用方法

```tsx
import Mem0StatsDashboard from '@/components/mem0/Mem0StatsDashboard';

function App() {
  return (
    <div>
      <Mem0StatsDashboard />
    </div>
  );
}
```

#### 特性

- **实时数据更新**：自动获取最新的系统统计信息
- **趋势指示器**：显示数据变化趋势和百分比
- **错误处理**：优雅处理API错误，提供重试机制
- **响应式设计**：适配不同屏幕尺寸
- **Mem0品牌色彩**：使用统一的品牌色彩方案

#### 数据结构

```typescript
interface StatsData {
  totalMemories: number;      // 总记忆数
  activeUsers: number;        // 活跃用户数
  todayOperations: number;    // 今日操作数
  avgResponseTime: number;    // 平均响应时间(ms)
  trends: {
    memories: TrendData;
    operations: TrendData;
    responseTime: TrendData;
    users: TrendData;
  };
}

interface TrendData {
  value: number;              // 变化值
  isPositive: boolean;        // 是否为正向变化
}
```

#### 样式定制

组件使用Tailwind CSS类进行样式定制，主要颜色变量：

```css
--mem0-primary: #00d4aa;     /* Mem0品牌主色 */
--zinc-900: #18181b;         /* 深色背景 */
--zinc-800: #27272a;         /* 卡片背景 */
```

### 2. ActivityTimeline - 活动时间线组件

#### 功能描述

ActivityTimeline组件基于记忆历史数据构建活动时间线，支持多维度过滤和实时更新。

#### 使用方法

```tsx
import ActivityTimeline from '@/components/mem0/ActivityTimeline';

function App() {
  return (
    <div>
      <ActivityTimeline 
        className="custom-timeline"
        autoRefresh={true}
        refreshInterval={30000}
      />
    </div>
  );
}
```

#### Props

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| className | string | '' | 自定义CSS类名 |
| autoRefresh | boolean | true | 是否自动刷新 |
| refreshInterval | number | 30000 | 刷新间隔(ms) |

#### 特性

- **多维度过滤**：支持按用户、应用、智能体、分类过滤
- **实时更新**：可配置的自动刷新机制
- **空状态处理**：优雅的空数据展示
- **加载状态**：骨架屏加载效果
- **响应式设计**：移动端友好

#### 数据结构

```typescript
interface MemoryHistoryItem {
  id: string;                 // 历史记录ID
  memory_id: string;          // 关联的记忆ID
  operation: 'create' | 'update' | 'delete'; // 操作类型
  timestamp: string;          // 时间戳
  user_id: string;           // 用户ID
  run_id?: string;           // 运行ID
  agent_id?: string;         // 智能体ID
  categories: string[];       // 分类标签
}
```

### 3. QuickActions - 快速操作组件

#### 功能描述

QuickActions组件提供常用操作的快速入口，包括记忆管理、搜索、导出等功能。

#### 使用方法

```tsx
import QuickActions from '@/components/mem0/QuickActions';

function App() {
  const [currentUserId, setCurrentUserId] = useState('default');
  const [currentRunId, setCurrentRunId] = useState('');
  const [currentAgentId, setCurrentAgentId] = useState('default');

  return (
    <div>
      <QuickActions 
        currentUserId={currentUserId}
        currentRunId={currentRunId}
        currentAgentId={currentAgentId}
        onUserChange={setCurrentUserId}
        onRunChange={setCurrentRunId}
        onAgentChange={setCurrentAgentId}
      />
    </div>
  );
}
```

#### Props

| 属性 | 类型 | 描述 |
|------|------|------|
| currentUserId | string | 当前选中的用户ID |
| currentRunId | string | 当前选中的运行ID |
| currentAgentId | string | 当前选中的智能体ID |
| onUserChange | (userId: string) => void | 用户变更回调 |
| onRunChange | (runId: string) => void | 运行ID变更回调 |
| onAgentChange | (agentId: string) => void | 智能体变更回调 |

#### 特性

- **上下文管理**：用户、应用、智能体选择器
- **快速操作**：创建记忆、搜索、导出、批量删除、清除缓存
- **对话框交互**：模态对话框式的操作界面
- **状态持久化**：本地存储保存用户选择
- **表单验证**：输入验证和错误提示

#### 操作列表

1. **创建记忆**：快速创建新的记忆条目
2. **搜索记忆**：全文搜索现有记忆
3. **导出数据**：导出记忆数据为JSON格式
4. **批量删除**：批量删除选中的记忆
5. **清除缓存**：清除本地缓存数据

## 通用Hook

### useUserManagement

用户管理相关的Hook，提供用户列表获取和管理功能。

```typescript
import { useUserManagement } from '@/hooks/useUserManagement';

function Component() {
  const { users, currentUser, setCurrentUser, loading } = useUserManagement();
  
  return (
    <select value={currentUser} onChange={(e) => setCurrentUser(e.target.value)}>
      {users.map(user => (
        <option key={user} value={user}>{user}</option>
      ))}
    </select>
  );
}
```

### useAppManagement

应用管理相关的Hook，从记忆数据中推断应用列表。

```typescript
import { useAppManagement } from '@/hooks/useAppManagement';

function Component() {
  const { apps, currentApp, setCurrentApp, loading } = useAppManagement();
  
  // 使用方法同useUserManagement
}
```

### useAgentManagement

智能体管理相关的Hook，提供智能体选择和管理功能。

```typescript
import { useAgentManagement } from '@/hooks/useAgentManagement';

function Component() {
  const { agents, currentAgent, setCurrentAgent, loading } = useAgentManagement();
  
  // 使用方法同useUserManagement
}
```

## 样式指南

### 颜色规范

```css
/* Mem0品牌色彩 */
--mem0-primary: #00d4aa;
--mem0-primary-hover: #00c299;

/* 深色主题 */
--zinc-50: #fafafa;
--zinc-100: #f4f4f5;
--zinc-200: #e4e4e7;
--zinc-300: #d4d4d8;
--zinc-400: #a1a1aa;
--zinc-500: #71717a;
--zinc-600: #52525b;
--zinc-700: #3f3f46;
--zinc-800: #27272a;
--zinc-900: #18181b;
--zinc-950: #09090b;
```

### 组件间距

```css
/* 标准间距 */
.space-y-4 > * + * { margin-top: 1rem; }
.space-y-6 > * + * { margin-top: 1.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }

/* 内边距 */
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
```

### 响应式断点

```css
/* 移动端优先 */
@media (min-width: 640px) { /* sm */ }
@media (min-width: 768px) { /* md */ }
@media (min-width: 1024px) { /* lg */ }
@media (min-width: 1280px) { /* xl */ }
```

## 最佳实践

### 1. 组件使用

- 始终使用TypeScript进行类型检查
- 合理使用React.memo优化性能
- 遵循单一职责原则
- 保持组件的可复用性

### 2. 状态管理

- 使用useState管理组件内部状态
- 通过Props传递共享状态
- 使用localStorage持久化关键状态
- 避免过度的状态提升

### 3. 错误处理

- 实现优雅的错误边界
- 提供用户友好的错误信息
- 实现重试机制
- 记录错误日志用于调试

### 4. 性能优化

- 使用useMemo缓存计算结果
- 使用useCallback稳定函数引用
- 实现虚拟滚动处理大量数据
- 合理使用懒加载

## 故障排除

### 常见问题

1. **API调用失败**
   - 检查Mem0 Server是否正常运行
   - 验证API端点URL是否正确
   - 查看浏览器控制台错误信息

2. **组件不渲染**
   - 检查Props类型是否正确
   - 验证必需的依赖是否已安装
   - 查看React开发者工具

3. **样式不生效**
   - 确认Tailwind CSS已正确配置
   - 检查CSS类名是否正确
   - 验证样式优先级

### 调试技巧

```typescript
// 启用详细日志
localStorage.setItem('debug', 'mem0:*');

// 查看组件状态
console.log('Component state:', { users, currentUser, loading });

// 监控API调用
console.log('API call:', { endpoint, params, response });
```

## 更新日志

### v1.0.0 (2024-01-28)

- ✅ 初始版本发布
- ✅ 完成三大核心组件开发
- ✅ 实现API规范符合性修复
- ✅ 添加完整的TypeScript类型定义
- ✅ 实现响应式设计和深色主题
