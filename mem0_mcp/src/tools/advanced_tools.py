"""
Advanced memory management tools for Mem0 MCP server
"""

import json
import asyncio
from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timedelta

from .base_tool import BaseTool, ToolResult
from ..client.adapters import BaseAdapter
from ..utils.errors import ToolExecutionError
from ..utils.logger import get_logger

logger = get_logger(__name__)


class SelectiveMemoryTool(BaseTool):
    """Tool for intelligent selective memory management based on importance"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="selective_memory",
            description="Evaluate and selectively manage memories based on importance and relevance"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute selective memory operations"""
        try:
            await self.validate_arguments(arguments)
            
            # Get user identity from context or arguments
            identity = self.get_user_identity(arguments)
            
            # Extract operation type
            operation = arguments["operation"]
            
            # Build parameters for API call using resolved identity
            params = {}
            if identity.user_id:
                params["user_id"] = identity.user_id
            if identity.agent_id:
                params["agent_id"] = identity.agent_id
            if identity.run_id:
                params["run_id"] = identity.run_id
            
            self.logger.debug(f"Selective memory operation: {operation} for identity: {identity.get_primary_id()}")
            
            # Execute operation based on type
            if operation == "evaluate":
                result = await self._evaluate_importance(arguments, params)
            elif operation == "filter":
                result = await self._filter_by_importance(arguments, params)
            elif operation == "selective_add":
                result = await self._selective_add_memory(arguments, params)
            elif operation == "analyze":
                result = await self._analyze_memory_distribution(arguments, params)
            else:
                return ToolResult.error(f"Unknown operation: {operation}")
            
            return result
                
        except Exception as e:
            self.logger.error(f"Error in selective memory tool: {str(e)}")
            return ToolResult.error(str(e))
    
    async def _evaluate_importance(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Evaluate the importance of memory content before storage"""
        content = arguments["content"]
        importance_threshold = arguments.get("importance_threshold", 0.5)
        context = arguments.get("context", {})
        
        # Simulate importance evaluation algorithm
        # In a real implementation, this would use LLM or ML models
        importance_score = await self._calculate_importance_score(content, context)
        
        should_store = importance_score >= importance_threshold
        
        result_data = {
            "content": content,
            "importance_score": importance_score,
            "threshold": importance_threshold,
            "should_store": should_store,
            "evaluation_factors": self._get_evaluation_factors(content, context),
            "recommendation": self._get_storage_recommendation(importance_score, importance_threshold)
        }
        
        result_text = f"Memory Importance Evaluation:\n"
        result_text += f"Content: {content[:100]}{'...' if len(content) > 100 else ''}\n"
        result_text += f"Importance Score: {importance_score:.3f}\n"
        result_text += f"Threshold: {importance_threshold}\n"
        result_text += f"Recommendation: {'STORE' if should_store else 'SKIP'}\n"
        result_text += f"Factors: {', '.join(result_data['evaluation_factors'])}"
        
        content = [
            {"type": "text", "text": result_text},
            {"type": "text", "text": f"Full evaluation: {json.dumps(result_data, indent=2)}"}
        ]
        return ToolResult(content=content)
    
    async def _filter_by_importance(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Filter existing memories by importance criteria"""
        importance_threshold = arguments.get("importance_threshold", 0.7)
        limit = arguments.get("limit", 50)
        
        # First get all memories
        get_params = {**params, "limit": limit * 2}  # Get more to filter
        memories_response = await self.adapter.get_memories(**get_params)
        
        if isinstance(memories_response, dict) and "memories" in memories_response:
            memories = memories_response["memories"]
        elif isinstance(memories_response, list):
            memories = memories_response
        else:
            return ToolResult.error(f"Unexpected response format: {type(memories_response)}")
        
        # Evaluate importance for each memory and filter
        important_memories = []
        for memory in memories:
            content = memory.get("memory", memory.get("text", ""))
            if content:
                importance_score = await self._calculate_importance_score(content, memory)
                if importance_score >= importance_threshold:
                    memory["importance_score"] = importance_score
                    important_memories.append(memory)
        
        # Sort by importance
        important_memories.sort(key=lambda x: x["importance_score"], reverse=True)
        important_memories = important_memories[:limit]
        
        result_lines = [f"Found {len(important_memories)} important memories (threshold: {importance_threshold}):"]
        
        for i, memory in enumerate(important_memories[:10], 1):  # Show first 10
            memory_id = memory.get("id", "unknown")
            content = memory.get("memory", memory.get("text", ""))[:80]
            score = memory.get("importance_score", 0)
            
            result_lines.append(f"\n{i}. ID: {memory_id}")
            result_lines.append(f"   Content: {content}{'...' if len(content) == 80 else ''}")
            result_lines.append(f"   Importance: {score:.3f}")
        
        if len(important_memories) > 10:
            result_lines.append(f"\n... and {len(important_memories) - 10} more memories")
        
        content = [
            {"type": "text", "text": "\n".join(result_lines)},
            {"type": "text", "text": f"Filtered memories data: {json.dumps(important_memories, indent=2)}"}
        ]
        return ToolResult(content=content)
    
    async def _selective_add_memory(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Add memory only if it meets importance criteria"""
        messages = arguments["messages"]
        importance_threshold = arguments.get("importance_threshold", 0.6)
        force_add = arguments.get("force_add", False)
        
        # Extract content from messages for evaluation
        content_for_evaluation = "\n".join([
            msg.get("content", "") for msg in messages 
            if isinstance(msg.get("content"), str)
        ])
        
        # Evaluate importance
        importance_score = await self._calculate_importance_score(content_for_evaluation, arguments)
        
        if not force_add and importance_score < importance_threshold:
            result_text = f"Memory not added due to low importance score:\n"
            result_text += f"Score: {importance_score:.3f} < Threshold: {importance_threshold}\n"
            result_text += f"Content: {content_for_evaluation[:100]}{'...' if len(content_for_evaluation) > 100 else ''}\n"
            result_text += f"Use force_add=true to override this decision."
            
            return ToolResult.success(result_text)
        
        # Add memory using adapter
        add_params = {**params}
        
        # Add optional parameters
        optional_params = ["metadata", "custom_categories", "custom_instructions"]
        for param in optional_params:
            if param in arguments:
                add_params[param] = arguments[param]
        
        response = await self.adapter.add_memory(messages, **add_params)
        
        # Format response
        if isinstance(response, list) and len(response) > 0:
            memory = response[0]
            result_text = f"Memory added successfully with importance score {importance_score:.3f}\n"
            if isinstance(memory, dict) and "id" in memory:
                result_text += f"Memory ID: {memory['id']}\n"
            result_text += f"Threshold: {importance_threshold}\n"
            result_text += f"Force added: {force_add}"
            
            content = [
                {"type": "text", "text": result_text},
                {"type": "text", "text": f"Full response: {json.dumps(response, indent=2)}"}
            ]
            return ToolResult(content=content)
        else:
            return ToolResult.error("Failed to add memory")
    
    async def _analyze_memory_distribution(self, arguments: Dict[str, Any], params: Dict[str, Any]) -> ToolResult:
        """Analyze the distribution of memory importance scores"""
        limit = arguments.get("limit", 100)
        
        # Get memories for analysis
        get_params = {**params, "limit": limit}
        memories_response = await self.adapter.get_memories(**get_params)
        
        if isinstance(memories_response, dict) and "memories" in memories_response:
            memories = memories_response["memories"]
        elif isinstance(memories_response, list):
            memories = memories_response
        else:
            return ToolResult.error(f"Unexpected response format: {type(memories_response)}")
        
        if not memories:
            return ToolResult.success("No memories found for analysis.")
        
        # Calculate importance scores
        importance_scores = []
        for memory in memories:
            content = memory.get("memory", memory.get("text", ""))
            if content:
                score = await self._calculate_importance_score(content, memory)
                importance_scores.append(score)
        
        if not importance_scores:
            return ToolResult.success("No valid memories found for importance analysis.")
        
        # Calculate statistics
        avg_score = sum(importance_scores) / len(importance_scores)
        min_score = min(importance_scores)
        max_score = max(importance_scores)
        
        # Create distribution buckets
        buckets = {
            "very_low": sum(1 for s in importance_scores if s < 0.2),
            "low": sum(1 for s in importance_scores if 0.2 <= s < 0.4),
            "medium": sum(1 for s in importance_scores if 0.4 <= s < 0.6),
            "high": sum(1 for s in importance_scores if 0.6 <= s < 0.8),
            "very_high": sum(1 for s in importance_scores if s >= 0.8)
        }
        
        analysis_result = {
            "total_memories": len(memories),
            "analyzed_memories": len(importance_scores),
            "statistics": {
                "average_importance": round(avg_score, 3),
                "min_importance": round(min_score, 3),
                "max_importance": round(max_score, 3)
            },
            "distribution": buckets,
            "recommendations": self._get_cleanup_recommendations(buckets, avg_score)
        }
        
        result_text = f"Memory Importance Analysis:\n"
        result_text += f"Total memories analyzed: {len(importance_scores)}\n"
        result_text += f"Average importance: {avg_score:.3f}\n"
        result_text += f"Range: {min_score:.3f} - {max_score:.3f}\n\n"
        result_text += f"Distribution:\n"
        for category, count in buckets.items():
            percentage = (count / len(importance_scores)) * 100
            result_text += f"  {category.replace('_', ' ').title()}: {count} ({percentage:.1f}%)\n"
        
        if analysis_result["recommendations"]:
            result_text += f"\nRecommendations:\n"
            for rec in analysis_result["recommendations"]:
                result_text += f"  • {rec}\n"
        
        content = [
            {"type": "text", "text": result_text},
            {"type": "text", "text": f"Full analysis: {json.dumps(analysis_result, indent=2)}"}
        ]
        return ToolResult(content=content)
    
    async def _calculate_importance_score(self, content: str, context: Dict[str, Any] = None) -> float:
        """Calculate importance score for content (0.0 to 1.0)"""
        if not content or not content.strip():
            return 0.0
        
        # Simple heuristic-based importance calculation
        # In production, this would use LLM or ML models
        score = 0.0
        
        # Content length factor (longer content might be more important)
        length_factor = min(len(content) / 500, 1.0) * 0.1
        score += length_factor
        
        # Keyword importance (adjust weights as needed)
        important_keywords = {
            "important": 0.15, "critical": 0.2, "urgent": 0.15, "key": 0.1,
            "remember": 0.1, "note": 0.05, "todo": 0.1, "deadline": 0.15,
            "decision": 0.12, "meeting": 0.08, "project": 0.08, "goal": 0.1
        }
        
        content_lower = content.lower()
        for keyword, weight in important_keywords.items():
            if keyword in content_lower:
                score += weight
        
        # Recency factor (from context metadata if available)
        if context and isinstance(context, dict):
            created_at = context.get("created_at")
            if created_at:
                try:
                    # Assume ISO format or timestamp
                    if isinstance(created_at, str):
                        created_time = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    else:
                        created_time = datetime.fromtimestamp(created_at)
                    
                    days_old = (datetime.utcnow() - created_time).days
                    recency_factor = max(0, (7 - days_old) / 7) * 0.1  # More recent = more important
                    score += recency_factor
                except:
                    pass  # Ignore date parsing errors
        
        # Entities and proper nouns (simple heuristic)
        words = content.split()
        proper_nouns = sum(1 for word in words if word and word[0].isupper())
        entity_factor = min(proper_nouns / len(words), 0.3) * 0.15 if words else 0
        score += entity_factor
        
        # Question marks might indicate important inquiries
        question_factor = content.count('?') * 0.05
        score += min(question_factor, 0.1)
        
        # Normalize to [0, 1] range
        return min(max(score, 0.0), 1.0)
    
    def _get_evaluation_factors(self, content: str, context: Dict[str, Any]) -> List[str]:
        """Get list of factors that influenced importance evaluation"""
        factors = []
        
        if len(content) > 200:
            factors.append("substantial_content")
        
        important_keywords = ["important", "critical", "urgent", "remember", "decision", "deadline"]
        found_keywords = [kw for kw in important_keywords if kw in content.lower()]
        if found_keywords:
            factors.append(f"keywords: {', '.join(found_keywords)}")
        
        if '?' in content:
            factors.append("contains_questions")
        
        words = content.split()
        proper_nouns = sum(1 for word in words if word and word[0].isupper())
        if proper_nouns > len(words) * 0.1:
            factors.append("entity_rich")
        
        if context and context.get("created_at"):
            factors.append("recency_considered")
        
        return factors or ["basic_analysis"]
    
    def _get_storage_recommendation(self, score: float, threshold: float) -> str:
        """Get storage recommendation based on score"""
        if score >= threshold + 0.2:
            return "Highly recommended for storage"
        elif score >= threshold:
            return "Recommended for storage"
        elif score >= threshold - 0.1:
            return "Borderline - consider context"
        else:
            return "Not recommended for storage"
    
    def _get_cleanup_recommendations(self, distribution: Dict[str, int], avg_score: float) -> List[str]:
        """Get recommendations for memory cleanup"""
        recommendations = []
        total = sum(distribution.values())
        
        if distribution["very_low"] > total * 0.3:
            recommendations.append("Consider removing very low importance memories to free up space")
        
        if avg_score < 0.4:
            recommendations.append("Overall memory quality is low - review storage criteria")
        
        if distribution["very_high"] < total * 0.1:
            recommendations.append("Few high-importance memories found - consider adjusting importance thresholds")
        
        return recommendations
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for selective memory management"""
        from ..protocol.message_types import SELECTIVE_MEMORY_SCHEMA
        return SELECTIVE_MEMORY_SCHEMA


class CriteriaRetrievalTool(BaseTool):
    """Tool for advanced memory retrieval based on multiple criteria"""
    
    def __init__(self, adapter: BaseAdapter):
        super().__init__(
            name="criteria_retrieval",
            description="Retrieve memories using complex criteria and multi-dimensional filtering"
        )
        self.adapter = adapter
    
    async def execute(self, arguments: Dict[str, Any]) -> ToolResult:
        """Execute criteria-based retrieval operations"""
        try:
            await self.validate_arguments(arguments)
            
            # Get user identity from context or arguments
            identity = self.get_user_identity(arguments)
            
            # Extract criteria
            criteria = arguments["criteria"]
            
            # Build base parameters for API call using resolved identity
            params = {}
            if identity.user_id:
                params["user_id"] = identity.user_id
            if identity.agent_id:
                params["agent_id"] = identity.agent_id
            if identity.run_id:
                params["run_id"] = identity.run_id
            
            self.logger.debug(f"Criteria retrieval for identity: {identity.get_primary_id()}")
            self.logger.debug(f"Criteria: {criteria}")
            
            # Execute multi-stage retrieval
            result = await self._execute_criteria_retrieval(criteria, params, arguments)
            
            return result
                
        except Exception as e:
            self.logger.error(f"Error in criteria retrieval tool: {str(e)}")
            return ToolResult.error(str(e))
    
    async def _execute_criteria_retrieval(self, criteria: Dict[str, Any], params: Dict[str, Any], arguments: Dict[str, Any]) -> ToolResult:
        """Execute complex criteria-based retrieval"""
        
        # Stage 1: Base retrieval
        base_memories = await self._get_base_memories(criteria, params)
        
        if not base_memories:
            return ToolResult.success("No memories found matching base criteria.")
        
        # Stage 2: Apply filters
        filtered_memories = await self._apply_criteria_filters(base_memories, criteria)
        
        # Stage 3: Sort and rank
        sorted_memories = await self._sort_by_criteria(filtered_memories, criteria)
        
        # Stage 4: Apply final limits
        limit = arguments.get("limit", 50)
        final_memories = sorted_memories[:limit]
        
        # Stage 5: Generate analysis
        analysis = await self._generate_retrieval_analysis(final_memories, criteria, len(base_memories))
        
        # Format results
        result_text = self._format_retrieval_results(final_memories, analysis, criteria)
        
        content = [
            {"type": "text", "text": result_text},
            {"type": "text", "text": f"Detailed analysis: {json.dumps(analysis, indent=2)}"}
        ]
        return ToolResult(content=content)
    
    async def _get_base_memories(self, criteria: Dict[str, Any], params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get base set of memories for further filtering"""
        
        # Use search if query is provided
        if "query" in criteria and criteria["query"]:
            search_params = {**params, "query": criteria["query"]}
            
            # Add search parameters
            if "limit" in criteria:
                search_params["limit"] = min(criteria["limit"] * 2, 200)  # Get more for filtering
            
            response = await self.adapter.search_memories(**search_params)
            
            if isinstance(response, dict) and "memories" in response:
                return response["memories"]
            elif isinstance(response, list):
                return response
        
        # Fallback to get_memories
        get_params = {**params}
        if "limit" in criteria:
            get_params["limit"] = min(criteria["limit"] * 2, 200)
        
        response = await self.adapter.get_memories(**get_params)
        
        if isinstance(response, dict) and "memories" in response:
            return response["memories"]
        elif isinstance(response, list):
            return response
        
        return []
    
    async def _apply_criteria_filters(self, memories: List[Dict[str, Any]], criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Apply various filters based on criteria"""
        filtered = memories[:]
        
        # Time range filter
        if "time_range" in criteria:
            filtered = self._filter_by_time_range(filtered, criteria["time_range"])
        
        # Content length filter
        if "content_length" in criteria:
            filtered = self._filter_by_content_length(filtered, criteria["content_length"])
        
        # Keyword filters
        if "must_contain" in criteria:
            filtered = self._filter_by_keywords(filtered, criteria["must_contain"], must_contain=True)
        
        if "must_not_contain" in criteria:
            filtered = self._filter_by_keywords(filtered, criteria["must_not_contain"], must_contain=False)
        
        # Relevance threshold
        if "relevance_threshold" in criteria:
            filtered = await self._filter_by_relevance(filtered, criteria)
        
        # Custom metadata filters
        if "metadata_filters" in criteria:
            filtered = self._filter_by_metadata(filtered, criteria["metadata_filters"])
        
        return filtered
    
    def _filter_by_time_range(self, memories: List[Dict[str, Any]], time_range: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter memories by time range"""
        if not time_range:
            return memories
        
        filtered = []
        now = datetime.utcnow()
        
        # Parse time range
        start_time = None
        end_time = None
        
        if "start" in time_range:
            start_time = self._parse_time(time_range["start"], now)
        
        if "end" in time_range:
            end_time = self._parse_time(time_range["end"], now)
        
        for memory in memories:
            memory_time = self._get_memory_time(memory)
            if memory_time:
                if start_time and memory_time < start_time:
                    continue
                if end_time and memory_time > end_time:
                    continue
                filtered.append(memory)
            else:
                # If no timestamp, include by default
                filtered.append(memory)
        
        return filtered
    
    def _filter_by_content_length(self, memories: List[Dict[str, Any]], length_criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter memories by content length"""
        if not length_criteria:
            return memories
        
        filtered = []
        min_length = length_criteria.get("min", 0)
        max_length = length_criteria.get("max", float('inf'))
        
        for memory in memories:
            content = memory.get("memory", memory.get("text", ""))
            content_length = len(content)
            
            if min_length <= content_length <= max_length:
                filtered.append(memory)
        
        return filtered
    
    def _filter_by_keywords(self, memories: List[Dict[str, Any]], keywords: List[str], must_contain: bool = True) -> List[Dict[str, Any]]:
        """Filter memories by keyword presence"""
        if not keywords:
            return memories
        
        filtered = []
        keywords_lower = [kw.lower() for kw in keywords]
        
        for memory in memories:
            content = memory.get("memory", memory.get("text", "")).lower()
            
            if must_contain:
                # All keywords must be present
                if all(kw in content for kw in keywords_lower):
                    filtered.append(memory)
            else:
                # None of the keywords should be present
                if not any(kw in content for kw in keywords_lower):
                    filtered.append(memory)
        
        return filtered
    
    async def _filter_by_relevance(self, memories: List[Dict[str, Any]], criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter memories by relevance threshold"""
        threshold = criteria.get("relevance_threshold", 0.5)
        query = criteria.get("query", "")
        
        if not query:
            return memories
        
        filtered = []
        for memory in memories:
            # Simple relevance calculation (in production, use embedding similarity)
            relevance = self._calculate_relevance(memory, query)
            if relevance >= threshold:
                memory["relevance_score"] = relevance
                filtered.append(memory)
        
        return filtered
    
    def _filter_by_metadata(self, memories: List[Dict[str, Any]], metadata_filters: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter memories by metadata criteria"""
        if not metadata_filters:
            return memories
        
        filtered = []
        for memory in memories:
            metadata = memory.get("metadata", {})
            
            # Check all metadata filters
            matches = True
            for key, expected_value in metadata_filters.items():
                if key not in metadata:
                    matches = False
                    break
                
                actual_value = metadata[key]
                
                # Handle different comparison types
                if isinstance(expected_value, dict):
                    # Range or comparison operators
                    if "min" in expected_value and actual_value < expected_value["min"]:
                        matches = False
                        break
                    if "max" in expected_value and actual_value > expected_value["max"]:
                        matches = False
                        break
                elif actual_value != expected_value:
                    matches = False
                    break
            
            if matches:
                filtered.append(memory)
        
        return filtered
    
    async def _sort_by_criteria(self, memories: List[Dict[str, Any]], criteria: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Sort memories based on criteria"""
        sort_by = criteria.get("sort_by", "relevance")
        sort_order = criteria.get("sort_order", "desc")
        
        if sort_by == "relevance" and "relevance_score" in (memories[0] if memories else {}):
            memories.sort(key=lambda x: x.get("relevance_score", 0), reverse=(sort_order == "desc"))
        elif sort_by == "time":
            memories.sort(key=lambda x: self._get_memory_time(x) or datetime.min, reverse=(sort_order == "desc"))
        elif sort_by == "length":
            memories.sort(key=lambda x: len(x.get("memory", x.get("text", ""))), reverse=(sort_order == "desc"))
        elif sort_by == "importance":
            # Calculate importance on-the-fly if not present
            for memory in memories:
                if "importance_score" not in memory:
                    content = memory.get("memory", memory.get("text", ""))
                    memory["importance_score"] = await self._calculate_simple_importance(content)
            memories.sort(key=lambda x: x.get("importance_score", 0), reverse=(sort_order == "desc"))
        
        return memories
    
    async def _generate_retrieval_analysis(self, memories: List[Dict[str, Any]], criteria: Dict[str, Any], original_count: int) -> Dict[str, Any]:
        """Generate analysis of retrieval results"""
        
        analysis = {
            "total_found": len(memories),
            "original_count": original_count,
            "filter_efficiency": len(memories) / max(original_count, 1),
            "criteria_applied": list(criteria.keys()),
            "result_statistics": {},
            "recommendations": []
        }
        
        if memories:
            # Content length statistics
            lengths = [len(m.get("memory", m.get("text", ""))) for m in memories]
            analysis["result_statistics"]["content_length"] = {
                "avg": sum(lengths) / len(lengths),
                "min": min(lengths),
                "max": max(lengths)
            }
            
            # Time distribution
            times = [self._get_memory_time(m) for m in memories if self._get_memory_time(m)]
            if times:
                analysis["result_statistics"]["time_span"] = {
                    "earliest": min(times).isoformat(),
                    "latest": max(times).isoformat(),
                    "count_with_time": len(times)
                }
            
            # Relevance statistics (if available)
            relevance_scores = [m.get("relevance_score") for m in memories if m.get("relevance_score")]
            if relevance_scores:
                analysis["result_statistics"]["relevance"] = {
                    "avg": sum(relevance_scores) / len(relevance_scores),
                    "min": min(relevance_scores),
                    "max": max(relevance_scores)
                }
        
        # Generate recommendations
        if analysis["filter_efficiency"] < 0.1:
            analysis["recommendations"].append("Criteria too restrictive - consider relaxing filters")
        elif analysis["filter_efficiency"] > 0.8:
            analysis["recommendations"].append("Criteria may be too broad - consider adding more filters")
        
        if len(memories) == 0:
            analysis["recommendations"].append("No results found - try broader search terms or different criteria")
        
        return analysis
    
    def _format_retrieval_results(self, memories: List[Dict[str, Any]], analysis: Dict[str, Any], criteria: Dict[str, Any]) -> str:
        """Format the retrieval results for display"""
        
        result_lines = [f"Criteria-Based Memory Retrieval Results"]
        result_lines.append(f"Applied criteria: {', '.join(criteria.keys())}")
        result_lines.append(f"Found {len(memories)} memories (from {analysis['original_count']} total)")
        result_lines.append(f"Filter efficiency: {analysis['filter_efficiency']:.2%}")
        
        if memories:
            result_lines.append(f"\nTop Results:")
            
            for i, memory in enumerate(memories[:10], 1):  # Show first 10
                memory_id = memory.get("id", "unknown")
                content = memory.get("memory", memory.get("text", ""))[:100]
                
                result_lines.append(f"\n{i}. ID: {memory_id}")
                result_lines.append(f"   Content: {content}{'...' if len(content) == 100 else ''}")
                
                # Show additional scoring if available
                if "relevance_score" in memory:
                    result_lines.append(f"   Relevance: {memory['relevance_score']:.3f}")
                if "importance_score" in memory:
                    result_lines.append(f"   Importance: {memory['importance_score']:.3f}")
                
                # Show timestamp if available
                memory_time = self._get_memory_time(memory)
                if memory_time:
                    result_lines.append(f"   Time: {memory_time.strftime('%Y-%m-%d %H:%M')}")
            
            if len(memories) > 10:
                result_lines.append(f"\n... and {len(memories) - 10} more results")
        
        # Add statistics
        if analysis["result_statistics"]:
            result_lines.append(f"\nStatistics:")
            stats = analysis["result_statistics"]
            
            if "content_length" in stats:
                cl = stats["content_length"]
                result_lines.append(f"  Content length: avg={cl['avg']:.0f}, range={cl['min']}-{cl['max']}")
            
            if "relevance" in stats:
                rel = stats["relevance"]
                result_lines.append(f"  Relevance scores: avg={rel['avg']:.3f}, range={rel['min']:.3f}-{rel['max']:.3f}")
        
        # Add recommendations
        if analysis["recommendations"]:
            result_lines.append(f"\nRecommendations:")
            for rec in analysis["recommendations"]:
                result_lines.append(f"  • {rec}")
        
        return "\n".join(result_lines)
    
    def _parse_time(self, time_str: str, reference_time: datetime) -> datetime:
        """Parse time string (supports relative and absolute formats)"""
        if not time_str:
            return reference_time
        
        time_str = time_str.lower().strip()
        
        # Relative time parsing
        if "hour" in time_str:
            hours = int(''.join(filter(str.isdigit, time_str))) or 1
            return reference_time - timedelta(hours=hours)
        elif "day" in time_str:
            days = int(''.join(filter(str.isdigit, time_str))) or 1
            return reference_time - timedelta(days=days)
        elif "week" in time_str:
            weeks = int(''.join(filter(str.isdigit, time_str))) or 1
            return reference_time - timedelta(weeks=weeks)
        elif "month" in time_str:
            months = int(''.join(filter(str.isdigit, time_str))) or 1
            return reference_time - timedelta(days=months * 30)
        
        # Try to parse as ISO format
        try:
            return datetime.fromisoformat(time_str.replace('Z', '+00:00'))
        except:
            pass
        
        # Try common formats
        formats = ["%Y-%m-%d", "%Y-%m-%d %H:%M", "%Y-%m-%d %H:%M:%S"]
        for fmt in formats:
            try:
                return datetime.strptime(time_str, fmt)
            except:
                continue
        
        return reference_time
    
    def _get_memory_time(self, memory: Dict[str, Any]) -> Optional[datetime]:
        """Extract timestamp from memory"""
        for time_field in ["created_at", "timestamp", "updated_at"]:
            if time_field in memory:
                time_value = memory[time_field]
                if isinstance(time_value, str):
                    try:
                        # Handle timezone-aware strings
                        if time_value.endswith('Z'):
                            time_value = time_value[:-1] + '+00:00'
                        elif '+' not in time_value and 'T' in time_value:
                            time_value = time_value + '+00:00'
                        
                        parsed_time = datetime.fromisoformat(time_value)
                        # Convert to UTC naive datetime for consistency
                        if parsed_time.tzinfo is not None:
                            return parsed_time.replace(tzinfo=None)
                        return parsed_time
                    except:
                        continue
                elif isinstance(time_value, (int, float)):
                    try:
                        return datetime.fromtimestamp(time_value)
                    except:
                        continue
        return None
    
    def _calculate_relevance(self, memory: Dict[str, Any], query: str) -> float:
        """Calculate simple relevance score (0.0 to 1.0)"""
        content = memory.get("memory", memory.get("text", "")).lower()
        query_lower = query.lower()
        
        if not content or not query_lower:
            return 0.0
        
        # Simple keyword matching score
        query_words = query_lower.split()
        content_words = content.split()
        
        if not query_words or not content_words:
            return 0.0
        
        matches = sum(1 for word in query_words if word in content_words)
        return matches / len(query_words)
    
    async def _calculate_simple_importance(self, content: str) -> float:
        """Calculate simple importance score"""
        # Reuse the SelectiveMemoryTool logic
        selective_tool = SelectiveMemoryTool(self.adapter)
        return await selective_tool._calculate_importance_score(content)
    
    def get_input_schema(self) -> Dict[str, Any]:
        """Get input schema for criteria retrieval"""
        from ..protocol.message_types import CRITERIA_RETRIEVAL_SCHEMA
        return CRITERIA_RETRIEVAL_SCHEMA