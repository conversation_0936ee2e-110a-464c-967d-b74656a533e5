"""
MCP protocol handler for processing MCP-specific messages
"""

from typing import Any, Dict, List, Optional, Tuple
from ..config.constants import MCP_VERSION, ERROR_CODES, SUPPORTED_MCP_VERSIONS
from ..utils.errors import ProtocolError
from ..utils.logger import get_logger
from .jsonrpc import <PERSON>SONRPCRequest, JSONRPCResponse, JSONRPCHandler, JSONRPCError
from .message_types import (
    MCPMessageType,
    InitializeRequest,
    InitializeResponse,
    ServerInfo,
    ServerCapabilities,
    ClientCapabilities,
    ToolsListRequest,
    ToolsListResponse,
    ToolInfo,
    ToolsCallRequest,
    ToolsCallResponse,
    ADD_MEMORY_SCHEMA,
    SEARCH_MEMORIES_SCHEMA,
    GET_MEMORIES_SCHEMA,
    GET_MEMORY_BY_ID_SCHEMA,
    DELETE_MEMORY_SCHEMA,
    UPDATE_MEMORY_SCHEMA,
    BATCH_DELETE_MEMORIES_SCHEMA,
    <PERSON><PERSON><PERSON><PERSON>_ENTITY_SCHEMA,
    GRAPH_RELATIONSHIP_SCHEMA,
    GRAPH_SEARCH_SCHEMA,
    SELECTIVE_MEMORY_SCHEMA,
    CRITERIA_RETRIEVAL_SCHEMA
)

logger = get_logger(__name__)


class MCPProtocolHandler:
    """
    Handler for MCP protocol messages and lifecycle
    """
    
    def __init__(self, server_name: str = "mem0-mcp-server", server_version: str = "1.0.0"):
        self.server_name = server_name
        self.server_version = server_version
        self.initialized = False
        self.client_capabilities: Optional[ClientCapabilities] = None
        
        # Define available tools - All 12 tools for complete MCP integration
        self.tools = [
            # Basic Memory Management Tools (7)
            ToolInfo(
                name="add_memory",
                description="💾 智能记忆添加 - 从对话消息中提取并存储记忆，支持自定义分类、时间戳、图谱增强等高级功能",
                inputSchema=ADD_MEMORY_SCHEMA
            ),
            ToolInfo(
                name="search_memories",
                description="🔍 智能记忆搜索 - 使用自然语言查询记忆，支持关键词搜索、语义检索、重排序和图谱关联",
                inputSchema=SEARCH_MEMORIES_SCHEMA
            ),
            ToolInfo(
                name="get_memories",
                description="📋 记忆列表获取 - 按用户、代理或会话获取记忆列表，支持分页和元数据过滤",
                inputSchema=GET_MEMORIES_SCHEMA
            ),
            ToolInfo(
                name="get_memory_by_id",
                description="🎯 精确记忆检索 - 通过唯一ID获取指定记忆的完整详情和元数据",
                inputSchema=GET_MEMORY_BY_ID_SCHEMA
            ),
            ToolInfo(
                name="delete_memory",
                description="🗑️ 记忆删除 - 安全删除指定ID的记忆，支持软删除和永久删除模式",
                inputSchema=DELETE_MEMORY_SCHEMA
            ),
            ToolInfo(
                name="update_memory",
                description="✏️ 记忆内容更新 - 修改现有记忆的内容、元数据和分类信息",
                inputSchema=UPDATE_MEMORY_SCHEMA
            ),
            ToolInfo(
                name="batch_delete_memories",
                description="🧹 批量记忆清理 - 按用户、代理或会话批量删除相关记忆，支持条件过滤",
                inputSchema=BATCH_DELETE_MEMORIES_SCHEMA
            ),
            
            # Graph Database Management Tools (3)
            ToolInfo(
                name="manage_graph_entities",
                description="🌐 图谱实体管理 - 创建、查询、更新和删除知识图谱中的实体节点，构建智能关系网络",
                inputSchema=GRAPH_ENTITY_SCHEMA
            ),
            ToolInfo(
                name="manage_graph_relationships",
                description="🔗 图谱关系管理 - 管理实体间的关系连接，支持权重设置和属性配置，构建复杂知识图谱",
                inputSchema=GRAPH_RELATIONSHIP_SCHEMA
            ),
            ToolInfo(
                name="search_graph",
                description="🕸️ 图谱智能搜索 - 在知识图谱中进行高级搜索，发现实体关系和知识连接",
                inputSchema=GRAPH_SEARCH_SCHEMA
            ),
            
            # Advanced Memory Management Tools (2)
            ToolInfo(
                name="selective_memory",
                description="🧠 智能选择性记忆 - 基于重要性评估的智能记忆管理，自动过滤、评估和选择性存储重要信息",
                inputSchema=SELECTIVE_MEMORY_SCHEMA
            ),
            ToolInfo(
                name="criteria_retrieval",
                description="🎛️ 多维条件检索 - 使用复杂条件进行记忆检索，支持时间范围、内容长度、关键词等多维度过滤",
                inputSchema=CRITERIA_RETRIEVAL_SCHEMA
            )
        ]
        
        logger.info(f"MCP Protocol Handler initialized for {server_name} v{server_version}")
    
    def negotiate_protocol_version(self, client_version: str) -> str:
        """
        Negotiate protocol version with client
        
        Args:
            client_version: Client's requested protocol version
            
        Returns:
            Agreed protocol version
        """
        logger.debug(f"Negotiating protocol version. Client: {client_version}, Supported: {SUPPORTED_MCP_VERSIONS}")
        
        # If client version is supported, use it
        if client_version in SUPPORTED_MCP_VERSIONS:
            logger.info(f"Using client's protocol version: {client_version}")
            return client_version
        
        # Otherwise, use server's default and warn
        logger.warning(f"Client protocol version {client_version} not supported. Using server version {MCP_VERSION}")
        return MCP_VERSION
    
    def get_server_capabilities(self) -> ServerCapabilities:
        """Get server capabilities"""
        return ServerCapabilities(
            tools={},      # Empty dict indicates tools are supported
            resources={},  # Empty dict indicates no resources supported
            prompts={},    # Empty dict indicates no prompts supported
            logging={}     # Empty dict indicates no logging callbacks supported
        )
    
    def handle_initialize(self, request: JSONRPCRequest) -> JSONRPCResponse:
        """
        Handle initialize request
        
        Args:
            request: Initialize request
            
        Returns:
            Initialize response
        """
        try:
            if not request.params or not isinstance(request.params, dict):
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "Initialize request must have params object",
                    id=request.id
                )
            
            # Parse client info and capabilities
            protocol_version = request.params.get("protocolVersion")
            if not protocol_version:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "protocolVersion is required",
                    id=request.id
                )
            
            # Negotiate protocol version
            agreed_version = self.negotiate_protocol_version(protocol_version)
            
            client_info = request.params.get("clientInfo", {})
            client_capabilities = request.params.get("capabilities", {})
            
            # Store client capabilities
            self.client_capabilities = ClientCapabilities(
                sampling=client_capabilities.get("sampling"),
                experimental=client_capabilities.get("experimental")
            )
            
            # Create response
            response = InitializeResponse(
                protocolVersion=agreed_version,
                capabilities=self.get_server_capabilities(),
                serverInfo=ServerInfo(
                    name=self.server_name,
                    version=self.server_version
                )
            )
            
            self.initialized = True
            logger.info(f"Initialized session with client: {client_info.get('name', 'unknown')}")
            
            return JSONRPCHandler.create_response(
                result=response.to_dict(),
                id=request.id
            )
            
        except Exception as e:
            logger.error(f"Error handling initialize: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error during initialization: {str(e)}",
                id=request.id
            )
    
    def handle_tools_list(self, request: JSONRPCRequest) -> JSONRPCResponse:
        """
        Handle tools/list request
        
        Args:
            request: Tools list request
            
        Returns:
            Tools list response
        """
        if not self.initialized:
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INVALID_REQUEST"],
                "Session not initialized",
                id=request.id
            )
        
        try:
            response = ToolsListResponse(tools=self.tools)
            
            logger.debug(f"Returning {len(self.tools)} available tools")
            
            return JSONRPCHandler.create_response(
                result=response.to_dict(),
                id=request.id
            )
            
        except Exception as e:
            logger.error(f"Error handling tools/list: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error listing tools: {str(e)}",
                id=request.id
            )
    
    async def handle_tools_call(self, request: JSONRPCRequest, tool_executor) -> JSONRPCResponse:
        """
        Handle tools/call request
        
        Args:
            request: Tools call request
            tool_executor: Tool executor instance
            
        Returns:
            Tools call response
        """
        if not self.initialized:
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INVALID_REQUEST"],
                "Session not initialized",
                id=request.id
            )
        
        try:
            if not request.params or not isinstance(request.params, dict):
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "Tools call request must have params object",
                    id=request.id
                )
            
            tool_name = request.params.get("name")
            arguments = request.params.get("arguments", {})
            
            # Handle special case: Cherry Studio might send tool parameters directly in params
            # without the standard MCP "name" and "arguments" structure
            if not tool_name and "query" in request.params:
                # This looks like a direct tool call, try to infer the tool name
                # For now, assume it's search_graph if query is present
                tool_name = "search_graph"
                arguments = request.params.copy()
                logger.info(f"Detected direct tool call format, inferred tool: {tool_name}")
            
            # Extract identity parameters from top-level params (Cherry Studio format)
            # and merge them into arguments for tool execution
            identity_params = ["user_id", "agent_id", "run_id", "session_id"]
            extracted_identity = {}
            for param in identity_params:
                if param in request.params and param not in arguments:
                    arguments[param] = request.params[param]
                    extracted_identity[param] = request.params[param]
            
            logger.info(f"Tool call: {tool_name}")
            logger.info(f"Original params: {request.params}")
            logger.info(f"Extracted identity: {extracted_identity}")
            logger.info(f"Final arguments: {arguments}")
            
            if not tool_name:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INVALID_PARAMS"],
                    "Tool name is required",
                    id=request.id
                )
            
            # Check if tool exists
            tool_names = [tool.name for tool in self.tools]
            if tool_name not in tool_names:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["METHOD_NOT_FOUND"],
                    f"Tool '{tool_name}' not found. Available tools: {', '.join(tool_names)}",
                    id=request.id
                )
            
            logger.debug(f"Executing tool: {tool_name} with arguments: {arguments}")
            
            # Execute tool asynchronously
            return await tool_executor.execute_tool(tool_name, arguments, request.id)
            
        except Exception as e:
            logger.error(f"Error handling tools/call: {str(e)}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["INTERNAL_ERROR"],
                f"Internal error executing tool: {str(e)}",
                id=request.id
            )
    
    async def handle_message(self, request: JSONRPCRequest, tool_executor=None) -> Optional[JSONRPCResponse]:
        """
        Route MCP message to appropriate handler
        
        Args:
            request: JSON-RPC request
            tool_executor: Tool executor for handling tool calls
            
        Returns:
            JSON-RPC response (None for notifications)
        """
        method = request.method
        
        # Handle notifications (no response expected)
        if JSONRPCHandler.is_notification(request):
            if method == MCPMessageType.INITIALIZED.value:
                logger.info("Client sent initialized notification")
                return None
            elif method == "notifications/initialized":
                logger.info("Client sent notifications/initialized")
                return None
            else:
                logger.debug(f"Unhandled notification method: {method}")
                return None
        
        # Handle requests (response expected)
        if method == MCPMessageType.INITIALIZE.value:
            return self.handle_initialize(request)
        
        elif method == MCPMessageType.TOOLS_LIST.value:
            return self.handle_tools_list(request)
        
        elif method == MCPMessageType.TOOLS_CALL.value:
            if tool_executor is None:
                return JSONRPCHandler.create_error_response(
                    ERROR_CODES["INTERNAL_ERROR"],
                    "Tool executor not available",
                    id=request.id
                )
            return await self.handle_tools_call(request, tool_executor)
        
        else:
            logger.warning(f"Unknown method: {method}")
            return JSONRPCHandler.create_error_response(
                ERROR_CODES["METHOD_NOT_FOUND"],
                f"Method '{method}' not found",
                id=request.id
            )