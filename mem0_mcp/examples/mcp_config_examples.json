{"mcpServers": {"mem0-mcp": {"type": "streamable-http", "url": "http://localhost:8001/mcp", "description": "Standard MCP endpoint - requires identity in tool arguments"}, "mem0-mcp-with-user": {"type": "streamable-http", "url": "http://localhost:8001/claude-code/mcp/user123", "description": "Context-aware endpoint with user identity in URL path"}, "mem0-mcp-with-agent": {"type": "streamable-http", "url": "http://localhost:8001/my-agent/mcp/agent/agent456", "description": "Agent-specific endpoint format"}, "mem0-mcp-with-run": {"type": "streamable-http", "url": "http://localhost:8001/workflow/mcp/run/run789", "description": "Run-specific endpoint format"}}}