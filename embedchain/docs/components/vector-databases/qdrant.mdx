---
title: Qdrant
---

In order to use Qdrant as a vector database, set the environment variables `QDRANT_URL` and `QDRANT_API_KEY` which you can find on [Qdrant Dashboard](https://cloud.qdrant.io/).

<CodeGroup>
```python main.py
from embedchain import App

# load qdrant configuration from yaml file
app = App.from_config(config_path="config.yaml")
```

```yaml config.yaml
vectordb:
  provider: qdrant
  config:
    collection_name: my_qdrant_index
```
</CodeGroup>

<Snippet file="missing-vector-db-tip.mdx" />
