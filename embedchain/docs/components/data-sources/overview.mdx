---
title: Overview
---

Embedchain comes with built-in support for various data sources. We handle the complexity of loading unstructured data from these data sources, allowing you to easily customize your app through a user-friendly interface.

<CardGroup cols={4}>
  <Card title="PDF file" href="/components/data-sources/pdf-file"></Card>
  <Card title="CSV file" href="/components/data-sources/csv"></Card>
  <Card title="JSON file" href="/components/data-sources/json"></Card>
  <Card title="Text" href="/components/data-sources/text"></Card>
  <Card title="Text File" href="/components/data-sources/text-file"></Card>
  <Card title="Directory" href="/components/data-sources/directory"></Card>
  <Card title="Web page" href="/components/data-sources/web-page"></Card>
  <Card title="Youtube Channel" href="/components/data-sources/youtube-channel"></Card>
  <Card title="Youtube Video" href="/components/data-sources/youtube-video"></Card>
  <Card title="Docs website" href="/components/data-sources/docs-site"></Card>
  <Card title="MDX file" href="/components/data-sources/mdx"></Card>
  <Card title="DOCX file" href="/components/data-sources/docx"></Card>
  <Card title="Notion" href="/components/data-sources/notion"></Card>
  <Card title="Sitemap" href="/components/data-sources/sitemap"></Card>
  <Card title="XML file" href="/components/data-sources/xml"></Card>
  <Card title="Q&A pair" href="/components/data-sources/qna"></Card>
  <Card title="OpenAPI" href="/components/data-sources/openapi"></Card>
  <Card title="Gmail" href="/components/data-sources/gmail"></Card>
  <Card title="Google Drive" href="/components/data-sources/google-drive"></Card>
  <Card title="GitHub" href="/components/data-sources/github"></Card>
  <Card title="Postgres" href="/components/data-sources/postgres"></Card>
  <Card title="MySQL" href="/components/data-sources/mysql"></Card>
  <Card title="Slack" href="/components/data-sources/slack"></Card>
  <Card title="Discord" href="/components/data-sources/discord"></Card>
  <Card title="Discourse" href="/components/data-sources/discourse"></Card>
  <Card title="Substack" href="/components/data-sources/substack"></Card>
  <Card title="Beehiiv" href="/components/data-sources/beehiiv"></Card>
  <Card title="Dropbox" href="/components/data-sources/dropbox"></Card>
  <Card title="Image" href="/components/data-sources/image"></Card>
  <Card title="Audio" href="/components/data-sources/audio"></Card>
  <Card title="Custom" href="/components/data-sources/custom"></Card>
</CardGroup>

<br/ >

<Snippet file="missing-data-source-tip.mdx" />
