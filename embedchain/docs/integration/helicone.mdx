---
title: "🧊 Helicone"
description: "Implement Helicone, the open-source LLM observability platform, with Embedchain. Monitor, debug, and optimize your AI applications effortlessly."
"twitter:title": "Helicone LLM Observability for Embedchain"
---

Get started with [Helicone](https://www.helicone.ai/), the open-source LLM observability platform for developers to monitor, debug, and optimize their applications.

To use Helicone, you need to do the following steps.

## Integration Steps

<Steps>
  <Step title="Create an account + Generate an API Key">
    Log into [Helicone](https://www.helicone.ai) or create an account. Once you have an account, you
    can generate an [API key](https://helicone.ai/developer).

    <Note>
      Make sure to generate a [write only API key](helicone-headers/helicone-auth).
    </Note>

  </Step>
  <Step title="Set base_url in the your code">
You can configure your base_url and OpenAI API key in your codebase
  <CodeGroup>

```python main.py
import os
from embedchain import App

# Modify the base path and add a Helicone URL
os.environ["OPENAI_API_BASE"] = "https://oai.helicone.ai/{YOUR_HELICONE_API_KEY}/v1"
# Add your OpenAI API Key
os.environ["OPENAI_API_KEY"] = "{YOUR_OPENAI_API_KEY}"

app = App()

# Add data to your app
app.add("https://en.wikipedia.org/wiki/Elon_Musk")

# Query your app
print(app.query("How many companies did Elon found? Which companies?"))
```

</CodeGroup>
  </Step>
<Step title="Now you can see all passing requests through Embedchain in Helicone">
    <img src="/images/helicone-embedchain.png" alt="Embedchain requests" />
  </Step>
</Steps>

Check out [Helicone](https://www.helicone.ai) to see more use cases!
