'use client';

import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { AppDispatch, RootState } from '@/store/store';
import { useGraphMemoryApi } from '@/hooks/useGraphMemoryApi';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  RefreshCw, 
  Search, 
  Filter, 
  Download, 
  Settings,
  Network,
  BarChart3,
  History,
  Users,
  GitBranch,
  AlertCircle,
  CheckCircle,
  Loader2
} from 'lucide-react';

// Graph Memory 组件导入
import { GraphVisualization } from '@/components/graph/GraphVisualization';
import { GraphMemoryFilters } from '@/components/graph/GraphMemoryFilters';
import { GraphStats } from '@/components/graph/GraphStats';
import { EntityPanel } from '@/components/graph/EntityPanel';
import { RelationshipPanel } from '@/components/graph/RelationshipPanel';
import { GraphHistory } from '@/components/graph/GraphHistory';

export default function GraphMemoryPage() {
  const dispatch = useDispatch<AppDispatch>();
  const {
    fetchGraphMemories,
    fetchGraphStats,
    isLoading,
    error,
    nodes,
    edges,
    stats,
    filters,
    viewState,
    hasUpdates
  } = useGraphMemoryApi();

  const [activeTab, setActiveTab] = useState('visualization');
  const [searchQuery, setSearchQuery] = useState('');
  const [isRefreshing, setIsRefreshing] = useState(false);

  // 加载初始数据
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        console.log('Loading initial graph data...');
        await Promise.all([
          fetchGraphMemories(),
          fetchGraphStats()
        ]);
        console.log('Initial graph data loaded successfully');
      } catch (error) {
        console.error('Failed to load initial graph data:', error);
      }
    };

    loadInitialData();
  }, [fetchGraphMemories, fetchGraphStats]);

  // 刷新数据
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await Promise.all([
        fetchGraphMemories(),
        fetchGraphStats()
      ]);
    } catch (error) {
      console.error('Failed to refresh data:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  // 搜索处理
  const handleSearch = async () => {
    if (!searchQuery.trim()) return;
    
    try {
      await fetchGraphMemories({
        ...filters,
        entity_search: searchQuery
      });
    } catch (error) {
      console.error('Search failed:', error);
    }
  };

  return (
    <div className="min-h-screen bg-zinc-950 text-zinc-100">
      <div className="container mx-auto px-4 py-8">
        {/* 页面标题和操作栏 */}
        <div className="mb-8">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-3xl font-bold mb-2 flex items-center gap-2">
                <Network className="h-8 w-8 text-blue-400" />
                Graph Memory
              </h1>
              <p className="text-zinc-400">可视化和管理记忆图谱，探索实体间的关系网络</p>
            </div>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isRefreshing}
                className="border-zinc-700 hover:bg-zinc-800"
              >
                <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
                刷新
              </Button>
              
              <Button
                variant="outline"
                size="sm"
                className="border-zinc-700 hover:bg-zinc-800"
              >
                <Download className="h-4 w-4 mr-2" />
                导出
              </Button>
            </div>
          </div>

          {/* 搜索栏 */}
          <div className="flex items-center gap-2 mb-4">
            <div className="flex-1 flex items-center gap-2">
              <Input
                placeholder="搜索实体或关系..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="bg-zinc-900 border-zinc-700 text-zinc-100"
              />
              <Button
                onClick={handleSearch}
                disabled={!searchQuery.trim() || isLoading}
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* 状态指示器 */}
          {error && (
            <Alert className="mb-4 border-red-800 bg-red-950/50">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription className="text-red-200">
                {error}
              </AlertDescription>
            </Alert>
          )}

          {isLoading && (
            <Alert className="mb-4 border-blue-800 bg-blue-950/50">
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertDescription className="text-blue-200">
                正在加载图数据...
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* 主要内容区域 */}
        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="grid w-full grid-cols-6 bg-zinc-900 border-zinc-700">
            <TabsTrigger value="visualization" className="data-[state=active]:bg-zinc-800">
              <Network className="h-4 w-4 mr-2" />
              可视化
            </TabsTrigger>
            <TabsTrigger value="entities" className="data-[state=active]:bg-zinc-800">
              <Users className="h-4 w-4 mr-2" />
              实体
            </TabsTrigger>
            <TabsTrigger value="relationships" className="data-[state=active]:bg-zinc-800">
              <GitBranch className="h-4 w-4 mr-2" />
              关系
            </TabsTrigger>
            <TabsTrigger value="statistics" className="data-[state=active]:bg-zinc-800">
              <BarChart3 className="h-4 w-4 mr-2" />
              统计
            </TabsTrigger>
            <TabsTrigger value="history" className="data-[state=active]:bg-zinc-800">
              <History className="h-4 w-4 mr-2" />
              历史
            </TabsTrigger>
            <TabsTrigger value="settings" className="data-[state=active]:bg-zinc-800">
              <Settings className="h-4 w-4 mr-2" />
              设置
            </TabsTrigger>
          </TabsList>

          {/* 图可视化标签页 */}
          <TabsContent value="visualization" className="space-y-6">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
              <div className="lg:col-span-3">
                <Card className="bg-zinc-900 border-zinc-800">
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Network className="h-5 w-5" />
                      图谱可视化
                    </CardTitle>
                    <CardDescription>
                      交互式图谱展示，支持缩放、拖拽和节点选择
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <GraphVisualization
                      className="w-full"
                      height="600px"
                    />
                  </CardContent>
                </Card>
              </div>
              
              <div className="space-y-6">
                <GraphMemoryFilters />
                <GraphStats />
              </div>
            </div>
          </TabsContent>

          {/* 实体管理标签页 */}
          <TabsContent value="entities">
            <EntityPanel />
          </TabsContent>

          {/* 关系管理标签页 */}
          <TabsContent value="relationships">
            <RelationshipPanel />
          </TabsContent>

          {/* 统计信息标签页 */}
          <TabsContent value="statistics">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <GraphStats />
            </div>
          </TabsContent>

          {/* 操作历史标签页 */}
          <TabsContent value="history">
            <GraphHistory />
          </TabsContent>

          {/* 设置标签页 */}
          <TabsContent value="settings">
            <Card className="bg-zinc-900 border-zinc-800">
              <CardHeader>
                <CardTitle>图谱设置</CardTitle>
                <CardDescription>
                  配置图谱显示和行为选项
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>布局算法</Label>
                  <select className="w-full p-2 bg-zinc-800 border border-zinc-700 rounded">
                    <option value="force">力导向布局</option>
                    <option value="hierarchical">层次布局</option>
                    <option value="circular">环形布局</option>
                  </select>
                </div>
                
                <div className="space-y-2">
                  <Label>节点大小</Label>
                  <input 
                    type="range" 
                    min="10" 
                    max="100" 
                    defaultValue="50"
                    className="w-full"
                  />
                </div>
                
                <div className="space-y-2">
                  <Label>边宽度</Label>
                  <input 
                    type="range" 
                    min="1" 
                    max="10" 
                    defaultValue="2"
                    className="w-full"
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
}
